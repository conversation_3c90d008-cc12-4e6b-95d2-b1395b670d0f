import { createClient } from '@supabase/supabase-js'
import { Database } from '../types/database'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key'

// 在开发环境中使用模拟数据，不抛出错误
const isDevelopment = process.env.NODE_ENV === 'development'

// 创建Supabase客户端，在开发环境中使用模拟配置
export const supabase = isDevelopment && (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)
  ? null // 在开发环境中如果没有配置则返回null
  : createClient<Database>(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      }
    })

// 辅助函数
export async function getCurrentUser() {
  if (!supabase) return null
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}

export async function signOut() {
  if (!supabase) return
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

export async function signInWithEmail(email: string, password: string) {
  if (!supabase) return { user: null, session: null }
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })
  if (error) throw error
  return data
}

export async function signUpWithEmail(email: string, password: string, metadata?: any) {
  if (!supabase) return { user: null, session: null }
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: metadata
    }
  })
  if (error) throw error
  return data
}

export async function resetPassword(email: string) {
  if (!supabase) return
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/auth/reset-password`,
  })
  if (error) throw error
}

// 服务相关函数
export async function getServices(filters?: {
  category?: string
  location?: string
  priceMin?: number
  priceMax?: number
  search?: string
  limit?: number
  offset?: number
}) {
  if (!supabase) {
    // 返回模拟数据
    return []
  }

  let query = supabase
    .from('services')
    .select(`
      *,
      provider:profiles(*)
    `)
    .eq('status', 'active')
    .order('created_at', { ascending: false })

  if (filters?.category) {
    query = query.eq('category', filters.category)
  }

  if (filters?.location) {
    query = query.ilike('location', `%${filters.location}%`)
  }

  if (filters?.priceMin) {
    query = query.gte('price', filters.priceMin)
  }

  if (filters?.priceMax) {
    query = query.lte('price', filters.priceMax)
  }

  if (filters?.search) {
    query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  if (filters?.offset) {
    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
  }

  const { data, error } = await query
  if (error) throw error
  return data
}

export async function getServiceById(id: string) {
  if (!supabase) {
    // 返回模拟数据
    return null
  }

  const { data, error } = await supabase
    .from('services')
    .select(`
      *,
      provider:profiles(*),
      reviews(*, user:profiles(*))
    `)
    .eq('id', id)
    .single()

  if (error) throw error
  return data
}

export async function createService(service: any) {
  if (!supabase) {
    return { id: 'mock-id', ...service }
  }

  const { data, error } = await supabase
    .from('services')
    .insert(service)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function updateService(id: string, updates: any) {
  if (!supabase) {
    return { id, ...updates }
  }

  const { data, error } = await supabase
    .from('services')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function deleteService(id: string) {
  if (!supabase) {
    return
  }

  const { error } = await supabase
    .from('services')
    .delete()
    .eq('id', id)

  if (error) throw error
}
