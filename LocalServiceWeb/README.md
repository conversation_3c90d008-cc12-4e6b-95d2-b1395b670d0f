# 本地生活服务平台 - Web版 🌐

基于LocalServiceApp移动应用开发的Next.js网站版本，提供更丰富的功能和更好的桌面端体验。

## 📱 项目概述

本项目是LocalServiceApp的网站版本，采用现代化的技术栈，提供完整的本地生活服务平台功能。网站版本不仅包含移动端的所有功能，还增加了更多适合桌面端的特性。

## ✨ 功能特性

### 🔐 用户系统
- 用户注册/登录
- 身份认证和资料管理
- 个人中心和仪表板
- 用户权限管理

### 🛍️ 服务管理
- 服务发布和编辑
- 服务分类浏览
- 高级搜索和筛选
- 服务详情展示
- 地图搜索功能

### 📋 订单系统
- 服务预订下单
- 订单状态管理
- 订单历史查看
- 订单评价系统

### 💳 支付系统
- 在线支付集成
- 支付记录管理
- 钱包功能

### 💬 消息系统
- 实时聊天功能
- 系统通知
- 消息中心

### ⭐ 评价系统
- 服务评价和评分
- 评价展示和管理
- 信誉体系

### 🎯 网站版增强功能
- **响应式设计** - 适配桌面、平板、手机
- **SEO优化** - 搜索引擎友好
- **数据可视化** - 统计图表和分析
- **管理后台** - 服务商管理面板
- **高级筛选** - 更丰富的搜索选项
- **地图集成** - 基于位置的服务搜索

## 🛠️ 技术栈

### 前端框架
- **Next.js 14** - React全栈框架 (App Router)
- **TypeScript** - 类型安全
- **Tailwind CSS** - 原子化CSS框架
- **Shadcn/ui** - 现代化UI组件库

### 状态管理
- **Zustand** - 轻量级状态管理
- **React Hook Form** - 表单处理
- **Zod** - 数据验证

### 后端服务
- **Supabase** - 数据库 + 认证 + 实时功能
- **PostgreSQL** - 关系型数据库

### UI/UX
- **Lucide React** - 图标库
- **Framer Motion** - 动画库
- **React Leaflet** - 地图组件
- **Recharts** - 图表库

### 开发工具
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **TypeScript** - 类型检查

## 🚀 快速开始

### 环境要求
- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 安装依赖
```bash
cd LocalServiceWeb
npm install
```

### 环境配置
1. 复制环境变量文件：
```bash
cp .env.local.example .env.local
```

2. 配置Supabase环境变量：
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本
```bash
npm run build
npm start
```

## 📁 项目结构

```
LocalServiceWeb/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # 认证相关页面组
│   ├── (dashboard)/              # 用户面板页面组
│   ├── services/                 # 服务相关页面
│   ├── globals.css               # 全局样式
│   ├── layout.tsx                # 根布局
│   └── page.tsx                  # 首页
├── components/                   # 组件目录
│   ├── ui/                       # 基础UI组件
│   ├── layout/                   # 布局组件
│   ├── home/                     # 首页组件
│   └── providers.tsx             # 全局Provider
├── lib/                          # 工具库
│   ├── supabase.ts              # Supabase配置
│   └── utils.ts                 # 工具函数
├── hooks/                        # 自定义Hooks
├── types/                        # TypeScript类型定义
└── public/                       # 静态资源
```

## 🎨 设计系统

### 颜色主题
- **主色调**: 蓝色系 (#2196F3)
- **辅助色**: 橙色系 (#FF6B6B)
- **成功色**: 绿色系 (#4CAF50)
- **警告色**: 黄色系 (#FFC107)
- **错误色**: 红色系 (#F44336)

### 响应式断点
- **sm**: 640px
- **md**: 768px
- **lg**: 1024px
- **xl**: 1280px
- **2xl**: 1536px

## 🔧 开发指南

### 代码规范
- 使用TypeScript进行类型检查
- 遵循ESLint规则
- 使用Prettier格式化代码
- 组件采用函数式组件 + Hooks

### 组件开发
- 使用Shadcn/ui作为基础组件
- 自定义组件放在对应功能目录
- 保持组件的单一职责原则

### 状态管理
- 全局状态使用Zustand
- 表单状态使用React Hook Form
- 服务端状态使用Supabase实时订阅

## 🚀 部署

### Vercel部署（推荐）
1. 连接GitHub仓库到Vercel
2. 配置环境变量
3. 自动部署

### 其他平台
- Netlify
- Railway
- 自托管服务器

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 📞 联系我们

- 项目地址: [GitHub](https://github.com/your-username/LocalServiceWeb)
- 问题反馈: [Issues](https://github.com/your-username/LocalServiceWeb/issues)
- 邮箱: <EMAIL>

---

**注意**: 这是LocalServiceApp的网站版本，与移动端应用共享相同的后端服务和数据库。
