export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          name: string
          avatar: string | null
          phone: string | null
          location: string | null
          bio: string | null
          is_verified: boolean
          rating: number
          total_services: number
          total_orders: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          name: string
          avatar?: string | null
          phone?: string | null
          location?: string | null
          bio?: string | null
          is_verified?: boolean
          rating?: number
          total_services?: number
          total_orders?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          avatar?: string | null
          phone?: string | null
          location?: string | null
          bio?: string | null
          is_verified?: boolean
          rating?: number
          total_services?: number
          total_orders?: number
          created_at?: string
          updated_at?: string
        }
      }
      services: {
        Row: {
          id: string
          title: string
          description: string
          category: string
          price: number
          price_unit: string
          location: string
          images: string[]
          tags: string[]
          features: string[]
          provider_id: string
          rating: number
          review_count: number
          order_count: number
          view_count: number
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          category: string
          price: number
          price_unit: string
          location: string
          images?: string[]
          tags?: string[]
          features?: string[]
          provider_id: string
          rating?: number
          review_count?: number
          order_count?: number
          view_count?: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          category?: string
          price?: number
          price_unit?: string
          location?: string
          images?: string[]
          tags?: string[]
          features?: string[]
          provider_id?: string
          rating?: number
          review_count?: number
          order_count?: number
          view_count?: number
          status?: string
          created_at?: string
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          service_id: string
          customer_id: string
          provider_id: string
          status: string
          scheduled_date: string
          price: number
          description: string
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          service_id: string
          customer_id: string
          provider_id: string
          status?: string
          scheduled_date: string
          price: number
          description: string
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          service_id?: string
          customer_id?: string
          provider_id?: string
          status?: string
          scheduled_date?: string
          price?: number
          description?: string
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      reviews: {
        Row: {
          id: string
          service_id: string
          order_id: string
          user_id: string
          rating: number
          comment: string
          images: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          service_id: string
          order_id: string
          user_id: string
          rating: number
          comment: string
          images?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          service_id?: string
          order_id?: string
          user_id?: string
          rating?: number
          comment?: string
          images?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
