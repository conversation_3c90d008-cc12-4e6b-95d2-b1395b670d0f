export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  phone?: string
  location?: string
  bio?: string
  isVerified: boolean
  rating: number
  totalServices: number
  totalOrders: number
  joinDate: string
  createdAt: string
  updatedAt: string
}

export interface Service {
  id: string
  title: string
  description: string
  category: string
  price: number
  priceUnit: 'hour' | 'day' | 'time' | 'project'
  location: string
  images: string[]
  tags: string[]
  features: string[]
  providerId: string
  provider?: User
  rating: number
  reviewCount: number
  orderCount: number
  viewCount: number
  status: 'active' | 'inactive' | 'pending'
  createdAt: string
  updatedAt: string
}

export interface Category {
  id: string
  name: string
  icon: string
  color: string
  description: string
  serviceCount: number
  slug: string
}

export interface Order {
  id: string
  serviceId: string
  service?: Service
  customerId: string
  customer?: User
  providerId: string
  provider?: User
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled'
  scheduledDate: string
  price: number
  description: string
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface Review {
  id: string
  serviceId: string
  service?: Service
  orderId: string
  order?: Order
  userId: string
  user?: User
  rating: number
  comment: string
  images?: string[]
  createdAt: string
  updatedAt: string
}

export interface Message {
  id: string
  conversationId: string
  senderId: string
  sender?: User
  receiverId: string
  receiver?: User
  content: string
  type: 'text' | 'image' | 'file'
  isRead: boolean
  createdAt: string
}

export interface Conversation {
  id: string
  participants: string[]
  lastMessage?: Message
  lastMessageAt: string
  createdAt: string
}

export interface SearchFilters {
  category?: string
  location?: string
  priceMin?: number
  priceMax?: number
  rating?: number
  sortBy?: 'newest' | 'price_low' | 'price_high' | 'rating' | 'popular'
  search?: string
}

export interface PaginationParams {
  page: number
  limit: number
}

export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface Stats {
  totalServices: number
  totalProviders: number
  totalOrders: number
  totalUsers: number
  averageRating: number
}

export interface Notification {
  id: string
  userId: string
  title: string
  message: string
  type: 'order' | 'message' | 'review' | 'system'
  isRead: boolean
  data?: any
  createdAt: string
}
