import Link from 'next/link'
import { Facebook, Twitter, Instagram, Mail, Phone, MapPin } from 'lucide-react'

export function Footer() {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    services: [
      { name: '家庭维修', href: '/categories/home-repair' },
      { name: '清洁服务', href: '/categories/cleaning' },
      { name: '美容美发', href: '/categories/beauty' },
      { name: '家教培训', href: '/categories/education' },
      { name: '宠物服务', href: '/categories/pet' },
      { name: '搬家服务', href: '/categories/moving' },
    ],
    company: [
      { name: '关于我们', href: '/about' },
      { name: '联系我们', href: '/contact' },
      { name: '加入我们', href: '/careers' },
      { name: '媒体报道', href: '/press' },
      { name: '投资者关系', href: '/investors' },
    ],
    support: [
      { name: '帮助中心', href: '/help' },
      { name: '服务条款', href: '/terms' },
      { name: '隐私政策', href: '/privacy' },
      { name: '安全保障', href: '/security' },
      { name: '意见反馈', href: '/feedback' },
    ],
    provider: [
      { name: '成为服务商', href: '/become-provider' },
      { name: '服务商中心', href: '/provider-center' },
      { name: '服务商指南', href: '/provider-guide' },
      { name: '服务商培训', href: '/provider-training' },
      { name: '服务商政策', href: '/provider-policy' },
    ],
  }

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-white font-bold text-sm">本</span>
              </div>
              <span className="font-bold text-lg">本地生活服务</span>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              专业可靠的本地生活服务平台，连接优质服务商与用户，让生活更美好。
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-semibold text-lg mb-4">热门服务</h3>
            <ul className="space-y-2">
              {footerLinks.services.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-semibold text-lg mb-4">公司信息</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="font-semibold text-lg mb-4">客户支持</h3>
            <ul className="space-y-2">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Provider */}
          <div>
            <h3 className="font-semibold text-lg mb-4">服务商</h3>
            <ul className="space-y-2">
              {footerLinks.provider.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Contact Info */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-400">
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4" />
              <span>客服热线：************</span>
            </div>
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4" />
              <span>邮箱：<EMAIL></span>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4" />
              <span>地址：北京市朝阳区xxx大厦</span>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
          <p>© {currentYear} 本地生活服务平台. 保留所有权利.</p>
          <p className="mt-2">
            <Link href="/terms" className="hover:text-white transition-colors">
              服务条款
            </Link>
            {' | '}
            <Link href="/privacy" className="hover:text-white transition-colors">
              隐私政策
            </Link>
            {' | '}
            <Link href="/sitemap" className="hover:text-white transition-colors">
              网站地图
            </Link>
          </p>
        </div>
      </div>
    </footer>
  )
}
