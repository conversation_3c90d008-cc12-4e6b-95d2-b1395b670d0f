import Link from 'next/link'
import { ArrowRight, Smartphone, Users, Star, Shield } from 'lucide-react'
import { Button } from '../ui/button'

export function CTASection() {
  const benefits = [
    {
      icon: <Users className="h-6 w-6" />,
      title: '海量服务商',
      description: '5000+认证服务商为您服务'
    },
    {
      icon: <Star className="h-6 w-6" />,
      title: '品质保证',
      description: '严格筛选，确保服务质量'
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: '安全保障',
      description: '平台担保，服务无忧'
    },
    {
      icon: <Smartphone className="h-6 w-6" />,
      title: '便捷预约',
      description: '随时随地，一键预约'
    }
  ]

  return (
    <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-700 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
      
      <div className="relative container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            开始您的服务之旅
          </h2>
          <p className="text-xl md:text-2xl text-blue-100 mb-12 leading-relaxed">
            无论您是寻找服务还是提供服务，我们都是您最好的选择
          </p>

          {/* Benefits Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              >
                <div className="text-orange-400 mb-3 flex justify-center">
                  {benefit.icon}
                </div>
                <h3 className="font-semibold text-lg mb-2">{benefit.title}</h3>
                <p className="text-blue-100 text-sm">{benefit.description}</p>
              </div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button size="lg" className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 text-lg" asChild>
              <Link href="/services">
                寻找服务
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="border-white text-blue-500 hover:bg-white hover:text-blue-600 px-8 py-4 text-lg"
              asChild
            >
              <Link href="/become-provider">
                成为服务商
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>

          {/* App Download Section */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="flex flex-col lg:flex-row items-center justify-between">
              <div className="text-left mb-6 lg:mb-0">
                <h3 className="text-2xl font-bold mb-2">下载移动应用</h3>
                <p className="text-blue-100 mb-4">随时随地享受便捷服务</p>
                <div className="flex space-x-4">
                  <Button variant="outline" className="border-white text-blue-500 hover:bg-white hover:text-blue-600">
                    <Smartphone className="mr-2 h-4 w-4" />
                    iOS 下载
                  </Button>
                  <Button variant="outline" className="border-white text-blue-500 hover:bg-white hover:text-blue-600">
                    <Smartphone className="mr-2 h-4 w-4" />
                    Android 下载
                  </Button>
                </div>
              </div>
              <div className="flex-shrink-0">
                <div className="w-32 h-32 bg-white/20 rounded-2xl flex items-center justify-center">
                  <Smartphone className="h-16 w-16 text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="mt-12 text-center">
            <p className="text-blue-100 mb-4">
              有疑问？联系我们的客服团队
            </p>
            <div className="flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0 sm:space-x-8 text-sm">
              <div>
                <span className="font-medium">客服热线：</span>
                <a href="tel:************" className="hover:text-orange-400 transition-colors">
                  ************
                </a>
              </div>
              <div>
                <span className="font-medium">邮箱：</span>
                <a href="mailto:<EMAIL>" className="hover:text-orange-400 transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div>
                <span className="font-medium">工作时间：</span>
                <span>7×24小时</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg
          className="w-full h-12 text-white"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
        >
          <path
            d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
            opacity=".25"
            fill="currentColor"
          ></path>
          <path
            d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
            opacity=".5"
            fill="currentColor"
          ></path>
          <path
            d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
            fill="currentColor"
          ></path>
        </svg>
      </div>
    </section>
  )
}
