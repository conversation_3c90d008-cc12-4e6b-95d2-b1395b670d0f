import Link from 'next/link'
import Image from 'next/image'
import { Star, MapPin, Clock, Shield, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatPrice } from '@/lib/utils'

export function FeaturedServices() {
  const featuredServices = [
    {
      id: '1',
      title: '专业家电维修服务',
      description: '提供各类家电维修服务，包括空调、洗衣机、冰箱等。拥有10年维修经验，技术过硬，价格合理。',
      price: 80,
      priceUnit: '小时',
      rating: 4.8,
      reviewCount: 156,
      orderCount: 89,
      category: '家庭维修',
      location: '朝阳区',
      image: 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=400&h=300&fit=crop',
      tags: ['上门服务', '质量保证', '价格透明'],
      provider: {
        name: '张师傅',
        rating: 4.9,
        isVerified: true,
        avatar: '👨‍🔧'
      },
      features: ['免费上门检测', '原厂配件', '30天质保']
    },
    {
      id: '2',
      title: '深度清洁服务',
      description: '专业清洁团队，提供家庭、办公室深度清洁服务。使用环保清洁用品，细致入微，让您的空间焕然一新。',
      price: 120,
      priceUnit: '次',
      rating: 4.9,
      reviewCount: 203,
      orderCount: 145,
      category: '清洁服务',
      location: '海淀区',
      image: 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=300&fit=crop',
      tags: ['环保用品', '专业团队', '细致服务'],
      provider: {
        name: '李阿姨',
        rating: 4.8,
        isVerified: true,
        avatar: '👩‍💼'
      },
      features: ['环保清洁剂', '专业设备', '保险保障']
    },
    {
      id: '3',
      title: '上门理发服务',
      description: '专业理发师上门服务，方便快捷。提供男女老少各种发型设计，工具齐全，技术精湛。',
      price: 60,
      priceUnit: '次',
      rating: 4.7,
      reviewCount: 89,
      orderCount: 67,
      category: '美容美发',
      location: '西城区',
      image: 'https://images.unsplash.com/photo-**********-aceed7bb0fe3?w=400&h=300&fit=crop',
      tags: ['上门服务', '专业技术', '工具齐全'],
      provider: {
        name: '王师傅',
        rating: 4.6,
        isVerified: false,
        avatar: '👨‍🎨'
      },
      features: ['专业工具', '多种发型', '上门便民']
    },
    {
      id: '4',
      title: '小学数学家教',
      description: '资深数学老师，专注小学数学辅导。因材施教，耐心细致，帮助孩子提高数学成绩和学习兴趣。',
      price: 100,
      priceUnit: '小时',
      rating: 4.9,
      reviewCount: 78,
      orderCount: 56,
      category: '家教培训',
      location: '东城区',
      image: 'https://images.unsplash.com/photo-1509062522246-3755977927d7?w=400&h=300&fit=crop',
      tags: ['经验丰富', '因材施教', '提分明显'],
      provider: {
        name: '陈老师',
        rating: 4.9,
        isVerified: true,
        avatar: '👩‍🏫'
      },
      features: ['10年教龄', '一对一辅导', '免费试听']
    }
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            推荐服务
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            精选优质服务商，为您提供专业可靠的服务体验
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8 mb-8">
          {featuredServices.map((service) => (
            <Link key={service.id} href={`/services/${service.id}`}>
              <Card className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 cursor-pointer overflow-hidden">
                <div className="relative">
                  <div className="aspect-video bg-gray-200 relative overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                      <span className="text-4xl opacity-50">📷</span>
                    </div>
                  </div>
                  <Badge className="absolute top-3 left-3 bg-primary">
                    {service.category}
                  </Badge>
                  {service.provider.isVerified && (
                    <Badge variant="secondary" className="absolute top-3 right-3 bg-green-500 text-white">
                      <Shield className="w-3 h-3 mr-1" />
                      认证
                    </Badge>
                  )}
                </div>

                <CardContent className="p-6">
                  {/* Service Header */}
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="font-semibold text-lg text-gray-900 group-hover:text-primary transition-colors line-clamp-2">
                      {service.title}
                    </h3>
                    <div className="text-right ml-4">
                      <div className="text-xl font-bold text-primary">
                        {formatPrice(service.price)}
                      </div>
                      <div className="text-sm text-gray-500">
                        /{service.priceUnit}
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {service.description}
                  </p>

                  {/* Provider Info */}
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm mr-3">
                      {service.provider.avatar}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className="font-medium text-sm">{service.provider.name}</span>
                        {service.provider.isVerified && (
                          <Shield className="w-3 h-3 text-green-500 ml-1" />
                        )}
                      </div>
                      <div className="flex items-center text-xs text-gray-500">
                        <Star className="w-3 h-3 text-yellow-400 mr-1" />
                        {service.provider.rating}
                      </div>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 mr-1" />
                      {service.rating} ({service.reviewCount}评价)
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      {service.location}
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {service.tags.slice(0, 3).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  {/* Features */}
                  <div className="space-y-1 mb-4">
                    {service.features.slice(0, 2).map((feature, index) => (
                      <div key={index} className="flex items-center text-xs text-gray-600">
                        <div className="w-1 h-1 bg-green-500 rounded-full mr-2"></div>
                        {feature}
                      </div>
                    ))}
                  </div>

                  {/* Action */}
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">
                      {service.orderCount}人已预订
                    </span>
                    <div className="flex items-center text-primary text-sm font-medium group-hover:text-primary-dark">
                      查看详情
                      <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Button size="lg" asChild>
            <Link href="/services">
              查看更多服务
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
