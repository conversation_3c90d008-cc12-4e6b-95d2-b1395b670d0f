import Link from 'next/link'
import { ArrowRight } from 'lucide-react'
import { But<PERSON> } from '../ui/button'
import { Card, CardContent } from '../ui/card'

export function CategorySection() {
  const categories = [
    {
      id: 'home-repair',
      name: '家庭维修',
      icon: '🔧',
      color: 'bg-red-100 text-red-600',
      count: 156,
      description: '家电维修、水电维修、门窗维修',
      popular: ['空调维修', '洗衣机维修', '水管维修']
    },
    {
      id: 'cleaning',
      name: '清洁服务',
      icon: '🧹',
      color: 'bg-blue-100 text-blue-600',
      count: 89,
      description: '家庭清洁、办公室清洁、深度清洁',
      popular: ['家庭保洁', '开荒清洁', '地毯清洗']
    },
    {
      id: 'beauty',
      name: '美容美发',
      icon: '💄',
      color: 'bg-pink-100 text-pink-600',
      count: 234,
      description: '理发、美容、美甲、化妆',
      popular: ['上门理发', '美甲服务', '化妆造型']
    },
    {
      id: 'education',
      name: '家教培训',
      icon: '📚',
      color: 'bg-green-100 text-green-600',
      count: 67,
      description: '学科辅导、技能培训、语言学习',
      popular: ['小学数学', '英语口语', '钢琴教学']
    },
    {
      id: 'pet',
      name: '宠物服务',
      icon: '🐕',
      color: 'bg-yellow-100 text-yellow-600',
      count: 45,
      description: '宠物寄养、美容、训练、医疗',
      popular: ['宠物寄养', '宠物美容', '遛狗服务']
    },
    {
      id: 'moving',
      name: '搬家服务',
      icon: '📦',
      color: 'bg-purple-100 text-purple-600',
      count: 78,
      description: '居民搬家、公司搬迁、物品包装',
      popular: ['居民搬家', '钢琴搬运', '办公室搬迁']
    },
    {
      id: 'photography',
      name: '摄影摄像',
      icon: '📷',
      color: 'bg-indigo-100 text-indigo-600',
      count: 123,
      description: '婚礼摄影、活动拍摄、证件照',
      popular: ['婚礼摄影', '活动拍摄', '产品摄影']
    },
    {
      id: 'other',
      name: '其他服务',
      icon: '⭐',
      color: 'bg-gray-100 text-gray-600',
      count: 234,
      description: '更多生活服务等您发现',
      popular: ['跑腿代办', '设备租赁', '活动策划']
    }
  ]

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            服务分类
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            涵盖生活各个方面的专业服务，总有一款适合您
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {categories.map((category) => (
            <Link key={category.id} href={`/categories/${category.id}`}>
              <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer h-full">
                <CardContent className="p-6">
                  <div className="text-center">
                    {/* Icon */}
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-full ${category.color} flex items-center justify-center text-2xl`}>
                      {category.icon}
                    </div>
                    
                    {/* Category Info */}
                    <h3 className="font-semibold text-lg text-gray-900 mb-2 group-hover:text-primary transition-colors">
                      {category.name}
                    </h3>
                    
                    <p className="text-sm text-gray-600 mb-3">
                      {category.description}
                    </p>
                    
                    <div className="text-xs text-gray-500 mb-4">
                      {category.count} 个服务
                    </div>
                    
                    {/* Popular Services */}
                    <div className="space-y-1">
                      {category.popular.map((service, index) => (
                        <div
                          key={index}
                          className="text-xs text-gray-500 bg-gray-100 rounded-full px-2 py-1 inline-block mr-1 mb-1"
                        >
                          {service}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Hover Arrow */}
                  <div className="flex justify-center mt-4 opacity-0 group-hover:opacity-100 transition-opacity">
                    <ArrowRight className="h-4 w-4 text-primary" />
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Button variant="outline" size="lg" asChild>
            <Link href="/categories">
              查看所有分类
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
