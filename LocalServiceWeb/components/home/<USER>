'use client'

import { useEffect, useState } from 'react'
import { Users, Briefcase, Star, ShoppingCart } from 'lucide-react'

export function StatsSection() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const element = document.getElementById('stats-section')
    if (element) {
      observer.observe(element)
    }

    return () => {
      if (element) {
        observer.unobserve(element)
      }
    }
  }, [])

  const stats = [
    {
      icon: <Users className="h-8 w-8" />,
      number: 50000,
      label: '注册用户',
      suffix: '+',
      color: 'text-blue-600'
    },
    {
      icon: <Briefcase className="h-8 w-8" />,
      number: 5000,
      label: '服务商',
      suffix: '+',
      color: 'text-green-600'
    },
    {
      icon: <ShoppingCart className="h-8 w-8" />,
      number: 100000,
      label: '完成订单',
      suffix: '+',
      color: 'text-purple-600'
    },
    {
      icon: <Star className="h-8 w-8" />,
      number: 4.8,
      label: '平均评分',
      suffix: '',
      color: 'text-yellow-600',
      decimal: true
    }
  ]

  const AnimatedNumber = ({ 
    number, 
    isVisible, 
    decimal = false 
  }: { 
    number: number
    isVisible: boolean
    decimal?: boolean 
  }) => {
    const [displayNumber, setDisplayNumber] = useState(0)

    useEffect(() => {
      if (!isVisible) return

      const duration = 2000 // 2 seconds
      const steps = 60
      const increment = number / steps
      let current = 0

      const timer = setInterval(() => {
        current += increment
        if (current >= number) {
          setDisplayNumber(number)
          clearInterval(timer)
        } else {
          setDisplayNumber(current)
        }
      }, duration / steps)

      return () => clearInterval(timer)
    }, [isVisible, number])

    if (decimal) {
      return <span>{displayNumber.toFixed(1)}</span>
    }

    return <span>{Math.floor(displayNumber).toLocaleString()}</span>
  }

  return (
    <section id="stats-section" className="py-16 bg-gradient-to-r from-blue-600 to-purple-700 text-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            平台数据
          </h2>
          <p className="text-xl text-blue-100 max-w-2xl mx-auto">
            数字见证我们的成长，用户信任我们的服务
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="text-center group"
            >
              <div className={`${stat.color} mb-4 flex justify-center transform group-hover:scale-110 transition-transform duration-300`}>
                {stat.icon}
              </div>
              <div className="text-4xl md:text-5xl font-bold mb-2">
                <AnimatedNumber 
                  number={stat.number} 
                  isVisible={isVisible}
                  decimal={stat.decimal}
                />
                <span className="text-3xl">{stat.suffix}</span>
              </div>
              <div className="text-blue-100 text-lg font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-16 text-center">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="font-semibold text-lg mb-2">覆盖城市</h3>
              <p className="text-blue-100">已覆盖全国50+个主要城市</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="font-semibold text-lg mb-2">服务类别</h3>
              <p className="text-blue-100">提供100+种生活服务类别</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="font-semibold text-lg mb-2">响应时间</h3>
              <p className="text-blue-100">平均30分钟内响应用户需求</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
