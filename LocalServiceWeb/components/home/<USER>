'use client'

import { useState, useEffect } from 'react'
import { Star, ChevronLeft, ChevronRight, Quote } from 'lucide-react'
import { Button } from '../ui/button'
import { Card, CardContent } from '../ui/card'

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)

  const testimonials = [
    {
      id: 1,
      name: '李女士',
      avatar: '👩',
      location: '北京朝阳区',
      rating: 5,
      service: '家电维修',
      comment: '师傅很专业，修好了我家的空调，价格也很合理。服务态度很好，会推荐给朋友！整个过程很顺利，从预约到完成维修只用了2小时。',
      date: '2024-01-10'
    },
    {
      id: 2,
      name: '王先生',
      avatar: '👨',
      location: '上海浦东区',
      rating: 5,
      service: '清洁服务',
      comment: '清洁阿姨非常细心，把家里打扫得干干净净，连犄角旮旯都不放过。用的清洁用品也很环保，没有刺激性气味。',
      date: '2024-01-08'
    },
    {
      id: 3,
      name: '张女士',
      avatar: '👩‍💼',
      location: '广州天河区',
      rating: 4,
      service: '美容美发',
      comment: '理发师技术很好，剪出来的发型很满意。上门服务很方便，不用跑理发店排队了。下次还会继续预约。',
      date: '2024-01-05'
    },
    {
      id: 4,
      name: '陈先生',
      avatar: '👨‍💻',
      location: '深圳南山区',
      rating: 5,
      service: '家教培训',
      comment: '老师很有耐心，孩子的数学成绩有了明显提高。教学方法很好，能够因材施教。孩子现在对数学更有兴趣了。',
      date: '2024-01-03'
    },
    {
      id: 5,
      name: '刘女士',
      avatar: '👩‍🦳',
      location: '成都锦江区',
      rating: 5,
      service: '宠物服务',
      comment: '宠物寄养服务很专业，狗狗在那里很开心。每天都会发照片和视频，让我很放心。环境很好，工作人员也很负责。',
      date: '2024-01-01'
    },
    {
      id: 6,
      name: '赵先生',
      avatar: '👨‍🔧',
      location: '杭州西湖区',
      rating: 4,
      service: '搬家服务',
      comment: '搬家师傅很专业，东西包装得很仔细，没有损坏。搬运过程很快，服务态度也很好。价格透明，没有额外收费。',
      date: '2023-12-28'
    }
  ]

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    )
  }

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    )
  }

  // Auto-play functionality
  useEffect(() => {
    const interval = setInterval(nextTestimonial, 5000)
    return () => clearInterval(interval)
  }, [])

  const getVisibleTestimonials = () => {
    const visible = []
    for (let i = 0; i < 3; i++) {
      const index = (currentIndex + i) % testimonials.length
      visible.push(testimonials[index])
    }
    return visible
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            用户评价
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            听听用户怎么说，真实评价见证服务品质
          </p>
        </div>

        {/* Desktop View - 3 cards */}
        <div className="hidden lg:block">
          <div className="grid grid-cols-3 gap-6 mb-8">
            {getVisibleTestimonials().map((testimonial, index) => (
              <Card key={testimonial.id} className={`transition-all duration-300 ${index === 1 ? 'scale-105 shadow-lg' : 'scale-95'}`}>
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-lg mr-4">
                      {testimonial.avatar}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                      <p className="text-sm text-gray-500">{testimonial.location}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center mb-3">
                    <div className="flex text-yellow-400 mr-2">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < testimonial.rating ? 'fill-current' : ''}`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">{testimonial.service}</span>
                  </div>
                  
                  <div className="relative">
                    <Quote className="absolute -top-2 -left-2 h-6 w-6 text-gray-300" />
                    <p className="text-gray-700 italic pl-4">
                      "{testimonial.comment}"
                    </p>
                  </div>
                  
                  <div className="text-xs text-gray-500 mt-4">
                    {testimonial.date}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Mobile View - 1 card */}
        <div className="lg:hidden">
          <div className="max-w-md mx-auto mb-8">
            <Card className="shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-lg mr-4">
                    {testimonials[currentIndex].avatar}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{testimonials[currentIndex].name}</h4>
                    <p className="text-sm text-gray-500">{testimonials[currentIndex].location}</p>
                  </div>
                </div>
                
                <div className="flex items-center mb-3">
                  <div className="flex text-yellow-400 mr-2">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${i < testimonials[currentIndex].rating ? 'fill-current' : ''}`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">{testimonials[currentIndex].service}</span>
                </div>
                
                <div className="relative">
                  <Quote className="absolute -top-2 -left-2 h-6 w-6 text-gray-300" />
                  <p className="text-gray-700 italic pl-4">
                    "{testimonials[currentIndex].comment}"
                  </p>
                </div>
                
                <div className="text-xs text-gray-500 mt-4">
                  {testimonials[currentIndex].date}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-center items-center space-x-4">
          <Button
            variant="outline"
            size="icon"
            onClick={prevTestimonial}
            className="rounded-full"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <div className="flex space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-primary' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
          
          <Button
            variant="outline"
            size="icon"
            onClick={nextTestimonial}
            className="rounded-full"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  )
}
