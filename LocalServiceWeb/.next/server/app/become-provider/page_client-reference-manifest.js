globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/become-provider/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/header.tsx":{"*":{"id":"(ssr)/./components/layout/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers.tsx":{"*":{"id":"(ssr)/./components/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/help/page.tsx":{"*":{"id":"(ssr)/./app/help/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/become-provider/page.tsx":{"*":{"id":"(ssr)/./app/become-provider/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/esm/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/layout/header.tsx":{"id":"(app-pages-browser)/./components/layout/header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/providers.tsx":{"id":"(app-pages-browser)/./components/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/ui/toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/help/page.tsx":{"id":"(app-pages-browser)/./app/help/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx":{"id":"(app-pages-browser)/./app/become-provider/page.tsx","name":"*","chunks":["app/become-provider/page","static/chunks/app/become-provider/page.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/AI/eian/app/LocalServiceWeb/":[],"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/layout":["static/css/app/layout.css"],"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/page":[],"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page":[]}}