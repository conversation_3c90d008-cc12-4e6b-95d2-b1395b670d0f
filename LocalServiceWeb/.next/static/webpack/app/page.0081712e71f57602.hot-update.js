"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/home/<USER>":
/*!**************************************************!*\
  !*** ./components/home/<USER>
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TestimonialsSection: function() { return /* binding */ TestimonialsSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ TestimonialsSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TestimonialsSection() {\n    _s();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const testimonials = [\n        {\n            id: 1,\n            name: \"李女士\",\n            avatar: \"\\uD83D\\uDC69\",\n            location: \"北京朝阳区\",\n            rating: 5,\n            service: \"家电维修\",\n            comment: \"师傅很专业，修好了我家的空调，价格也很合理。服务态度很好，会推荐给朋友！整个过程很顺利，从预约到完成维修只用了2小时。\",\n            date: \"2024-01-10\"\n        },\n        {\n            id: 2,\n            name: \"王先生\",\n            avatar: \"\\uD83D\\uDC68\",\n            location: \"上海浦东区\",\n            rating: 5,\n            service: \"清洁服务\",\n            comment: \"清洁阿姨非常细心，把家里打扫得干干净净，连犄角旮旯都不放过。用的清洁用品也很环保，没有刺激性气味。\",\n            date: \"2024-01-08\"\n        },\n        {\n            id: 3,\n            name: \"张女士\",\n            avatar: \"\\uD83D\\uDC69‍\\uD83D\\uDCBC\",\n            location: \"广州天河区\",\n            rating: 4,\n            service: \"美容美发\",\n            comment: \"理发师技术很好，剪出来的发型很满意。上门服务很方便，不用跑理发店排队了。下次还会继续预约。\",\n            date: \"2024-01-05\"\n        },\n        {\n            id: 4,\n            name: \"陈先生\",\n            avatar: \"\\uD83D\\uDC68‍\\uD83D\\uDCBB\",\n            location: \"深圳南山区\",\n            rating: 5,\n            service: \"家教培训\",\n            comment: \"老师很有耐心，孩子的数学成绩有了明显提高。教学方法很好，能够因材施教。孩子现在对数学更有兴趣了。\",\n            date: \"2024-01-03\"\n        },\n        {\n            id: 5,\n            name: \"刘女士\",\n            avatar: \"\\uD83D\\uDC69‍\\uD83E\\uDDB3\",\n            location: \"成都锦江区\",\n            rating: 5,\n            service: \"宠物服务\",\n            comment: \"宠物寄养服务很专业，狗狗在那里很开心。每天都会发照片和视频，让我很放心。环境很好，工作人员也很负责。\",\n            date: \"2024-01-01\"\n        },\n        {\n            id: 6,\n            name: \"赵先生\",\n            avatar: \"\\uD83D\\uDC68‍\\uD83D\\uDD27\",\n            location: \"杭州西湖区\",\n            rating: 4,\n            service: \"搬家服务\",\n            comment: \"搬家师傅很专业，东西包装得很仔细，没有损坏。搬运过程很快，服务态度也很好。价格透明，没有额外收费。\",\n            date: \"2023-12-28\"\n        }\n    ];\n    const nextTestimonial = ()=>{\n        setCurrentIndex((prevIndex)=>prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1);\n    };\n    const prevTestimonial = ()=>{\n        setCurrentIndex((prevIndex)=>prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1);\n    };\n    // Auto-play functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(nextTestimonial, 5000);\n        return ()=>clearInterval(interval);\n    }, []);\n    const getVisibleTestimonials = ()=>{\n        const visible = [];\n        for(let i = 0; i < 3; i++){\n            const index = (currentIndex + i) % testimonials.length;\n            visible.push(testimonials[index]);\n        }\n        return visible;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"用户评价\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"听听用户怎么说，真实评价见证服务品质\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden lg:block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-6 mb-8\",\n                        children: getVisibleTestimonials().map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"transition-all duration-300 \".concat(index === 1 ? \"scale-105 shadow-lg\" : \"scale-95\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-lg mr-4\",\n                                                    children: testimonial.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                    lineNumber: 120,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: testimonial.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                            lineNumber: 124,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: testimonial.location\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                            lineNumber: 125,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex text-yellow-400 mr-2\",\n                                                    children: [\n                                                        ...Array(5)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 \".concat(i < testimonial.rating ? \"fill-current\" : \"\")\n                                                        }, i, false, {\n                                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                            lineNumber: 132,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: testimonial.service\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                    lineNumber: 138,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"absolute -top-2 -left-2 h-6 w-6 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                    lineNumber: 142,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700 italic pl-4\",\n                                                    children: [\n                                                        '\"',\n                                                        testimonial.comment,\n                                                        '\"'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-4\",\n                                            children: testimonial.date\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                    lineNumber: 118,\n                                    columnNumber: 17\n                                }, this)\n                            }, testimonial.id, false, {\n                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-md mx-auto mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-lg mr-4\",\n                                                children: testimonials[currentIndex].avatar\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: testimonials[currentIndex].name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                        lineNumber: 167,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: testimonials[currentIndex].location\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex text-yellow-400 mr-2\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(i < testimonials[currentIndex].rating ? \"fill-current\" : \"\")\n                                                    }, i, false, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: testimonials[currentIndex].service\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"absolute -top-2 -left-2 h-6 w-6 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 italic pl-4\",\n                                                children: [\n                                                    '\"',\n                                                    testimonials[currentIndex].comment,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mt-4\",\n                                        children: testimonials[currentIndex].date\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"icon\",\n                            onClick: prevTestimonial,\n                            className: \"rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentIndex(index),\n                                    className: \"w-2 h-2 rounded-full transition-colors \".concat(index === currentIndex ? \"bg-primary\" : \"bg-gray-300\")\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"icon\",\n                            onClick: nextTestimonial,\n                            className: \"rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/components/home/<USER>",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n_s(TestimonialsSection, \"tPjzCc9H5UuFdWNuAHYoD0K4UOk=\");\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvaG9tZS90ZXN0aW1vbmlhbHMtc2VjdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQzBCO0FBQ2hDO0FBQ1M7QUFFdkMsU0FBU1M7O0lBQ2QsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR1gsK0NBQVFBLENBQUM7SUFFakQsTUFBTVksZUFBZTtRQUNuQjtZQUNFQyxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxTQUFTO1lBQ1RDLE1BQU07UUFDUjtRQUNBO1lBQ0VQLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsTUFBTTtRQUNSO1FBQ0E7WUFDRVAsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsVUFBVTtZQUNWQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsU0FBUztZQUNUQyxNQUFNO1FBQ1I7UUFDQTtZQUNFUCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxTQUFTO1lBQ1RDLE1BQU07UUFDUjtRQUNBO1lBQ0VQLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsTUFBTTtRQUNSO1FBQ0E7WUFDRVAsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsVUFBVTtZQUNWQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsU0FBUztZQUNUQyxNQUFNO1FBQ1I7S0FDRDtJQUVELE1BQU1DLGtCQUFrQjtRQUN0QlYsZ0JBQWdCLENBQUNXLFlBQ2ZBLGNBQWNWLGFBQWFXLE1BQU0sR0FBRyxJQUFJLElBQUlELFlBQVk7SUFFNUQ7SUFFQSxNQUFNRSxrQkFBa0I7UUFDdEJiLGdCQUFnQixDQUFDVyxZQUNmQSxjQUFjLElBQUlWLGFBQWFXLE1BQU0sR0FBRyxJQUFJRCxZQUFZO0lBRTVEO0lBRUEsMEJBQTBCO0lBQzFCckIsZ0RBQVNBLENBQUM7UUFDUixNQUFNd0IsV0FBV0MsWUFBWUwsaUJBQWlCO1FBQzlDLE9BQU8sSUFBTU0sY0FBY0Y7SUFDN0IsR0FBRyxFQUFFO0lBRUwsTUFBTUcseUJBQXlCO1FBQzdCLE1BQU1DLFVBQVUsRUFBRTtRQUNsQixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSSxHQUFHQSxJQUFLO1lBQzFCLE1BQU1DLFFBQVEsQ0FBQ3JCLGVBQWVvQixDQUFBQSxJQUFLbEIsYUFBYVcsTUFBTTtZQUN0RE0sUUFBUUcsSUFBSSxDQUFDcEIsWUFBWSxDQUFDbUIsTUFBTTtRQUNsQztRQUNBLE9BQU9GO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ0k7UUFBUUMsV0FBVTtrQkFDakIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNFOzRCQUFHRixXQUFVO3NDQUFvRDs7Ozs7O3NDQUdsRSw4REFBQ0c7NEJBQUVILFdBQVU7c0NBQTBDOzs7Ozs7Ozs7Ozs7OEJBTXpELDhEQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7a0NBQ1pOLHlCQUF5QlUsR0FBRyxDQUFDLENBQUNDLGFBQWFSLHNCQUMxQyw4REFBQ3hCLDBDQUFJQTtnQ0FBc0IyQixXQUFXLCtCQUFnRixPQUFqREgsVUFBVSxJQUFJLHdCQUF3QjswQ0FDekcsNEVBQUN2QixpREFBV0E7b0NBQUMwQixXQUFVOztzREFDckIsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVU7OERBQ1pLLFlBQVl4QixNQUFNOzs7Ozs7OERBRXJCLDhEQUFDb0I7O3NFQUNDLDhEQUFDSzs0REFBR04sV0FBVTtzRUFBK0JLLFlBQVl6QixJQUFJOzs7Ozs7c0VBQzdELDhEQUFDdUI7NERBQUVILFdBQVU7c0VBQXlCSyxZQUFZdkIsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUk5RCw4REFBQ21COzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVU7OERBQ1o7MkRBQUlPLE1BQU07cURBQUcsQ0FBQ0gsR0FBRyxDQUFDLENBQUNJLEdBQUdaLGtCQUNyQiw4REFBQzVCLCtHQUFJQTs0REFFSGdDLFdBQVcsV0FBd0QsT0FBN0NKLElBQUlTLFlBQVl0QixNQUFNLEdBQUcsaUJBQWlCOzJEQUQzRGE7Ozs7Ozs7Ozs7OERBS1gsOERBQUNhO29EQUFLVCxXQUFVOzhEQUF5QkssWUFBWXJCLE9BQU87Ozs7Ozs7Ozs7OztzREFHOUQsOERBQUNpQjs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUM3QiwrR0FBS0E7b0RBQUM2QixXQUFVOzs7Ozs7OERBQ2pCLDhEQUFDRztvREFBRUgsV0FBVTs7d0RBQTRCO3dEQUNyQ0ssWUFBWXBCLE9BQU87d0RBQUM7Ozs7Ozs7Ozs7Ozs7c0RBSTFCLDhEQUFDZ0I7NENBQUlELFdBQVU7c0RBQ1pLLFlBQVluQixJQUFJOzs7Ozs7Ozs7Ozs7K0JBaENabUIsWUFBWTFCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs4QkF5Qy9CLDhEQUFDc0I7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDM0IsMENBQUlBOzRCQUFDMkIsV0FBVTtzQ0FDZCw0RUFBQzFCLGlEQUFXQTtnQ0FBQzBCLFdBQVU7O2tEQUNyQiw4REFBQ0M7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDQztnREFBSUQsV0FBVTswREFDWnRCLFlBQVksQ0FBQ0YsYUFBYSxDQUFDSyxNQUFNOzs7Ozs7MERBRXBDLDhEQUFDb0I7O2tFQUNDLDhEQUFDSzt3REFBR04sV0FBVTtrRUFBK0J0QixZQUFZLENBQUNGLGFBQWEsQ0FBQ0ksSUFBSTs7Ozs7O2tFQUM1RSw4REFBQ3VCO3dEQUFFSCxXQUFVO2tFQUF5QnRCLFlBQVksQ0FBQ0YsYUFBYSxDQUFDTSxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTdFLDhEQUFDbUI7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDQztnREFBSUQsV0FBVTswREFDWjt1REFBSU8sTUFBTTtpREFBRyxDQUFDSCxHQUFHLENBQUMsQ0FBQ0ksR0FBR1osa0JBQ3JCLDhEQUFDNUIsK0dBQUlBO3dEQUVIZ0MsV0FBVyxXQUF1RSxPQUE1REosSUFBSWxCLFlBQVksQ0FBQ0YsYUFBYSxDQUFDTyxNQUFNLEdBQUcsaUJBQWlCO3VEQUQxRWE7Ozs7Ozs7Ozs7MERBS1gsOERBQUNhO2dEQUFLVCxXQUFVOzBEQUF5QnRCLFlBQVksQ0FBQ0YsYUFBYSxDQUFDUSxPQUFPOzs7Ozs7Ozs7Ozs7a0RBRzdFLDhEQUFDaUI7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDN0IsK0dBQUtBO2dEQUFDNkIsV0FBVTs7Ozs7OzBEQUNqQiw4REFBQ0c7Z0RBQUVILFdBQVU7O29EQUE0QjtvREFDckN0QixZQUFZLENBQUNGLGFBQWEsQ0FBQ1MsT0FBTztvREFBQzs7Ozs7Ozs7Ozs7OztrREFJekMsOERBQUNnQjt3Q0FBSUQsV0FBVTtrREFDWnRCLFlBQVksQ0FBQ0YsYUFBYSxDQUFDVSxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBUTFDLDhEQUFDZTtvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUM1Qiw4Q0FBTUE7NEJBQ0xzQyxTQUFROzRCQUNSQyxNQUFLOzRCQUNMQyxTQUFTdEI7NEJBQ1RVLFdBQVU7c0NBRVYsNEVBQUMvQiwrR0FBV0E7Z0NBQUMrQixXQUFVOzs7Ozs7Ozs7OztzQ0FHekIsOERBQUNDOzRCQUFJRCxXQUFVO3NDQUNadEIsYUFBYTBCLEdBQUcsQ0FBQyxDQUFDSSxHQUFHWCxzQkFDcEIsOERBQUNnQjtvQ0FFQ0QsU0FBUyxJQUFNbkMsZ0JBQWdCb0I7b0NBQy9CRyxXQUFXLDBDQUVWLE9BRENILFVBQVVyQixlQUFlLGVBQWU7bUNBSHJDcUI7Ozs7Ozs7Ozs7c0NBU1gsOERBQUN6Qiw4Q0FBTUE7NEJBQ0xzQyxTQUFROzRCQUNSQyxNQUFLOzRCQUNMQyxTQUFTekI7NEJBQ1RhLFdBQVU7c0NBRVYsNEVBQUM5QiwrR0FBWUE7Z0NBQUM4QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXBDO0dBbE9nQnpCO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvaG9tZS90ZXN0aW1vbmlhbHMtc2VjdGlvbi50c3g/MDRlNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgU3RhciwgQ2hldnJvbkxlZnQsIENoZXZyb25SaWdodCwgUXVvdGUgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICcuLi91aS9idXR0b24nXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCB9IGZyb20gJy4uL3VpL2NhcmQnXG5cbmV4cG9ydCBmdW5jdGlvbiBUZXN0aW1vbmlhbHNTZWN0aW9uKCkge1xuICBjb25zdCBbY3VycmVudEluZGV4LCBzZXRDdXJyZW50SW5kZXhdID0gdXNlU3RhdGUoMClcblxuICBjb25zdCB0ZXN0aW1vbmlhbHMgPSBbXG4gICAge1xuICAgICAgaWQ6IDEsXG4gICAgICBuYW1lOiAn5p2O5aWz5aOrJyxcbiAgICAgIGF2YXRhcjogJ/CfkaknLFxuICAgICAgbG9jYXRpb246ICfljJfkuqzmnJ3pmLPljLonLFxuICAgICAgcmF0aW5nOiA1LFxuICAgICAgc2VydmljZTogJ+WutueUtee7tOS/ricsXG4gICAgICBjb21tZW50OiAn5biI5YKF5b6I5LiT5Lia77yM5L+u5aW95LqG5oiR5a6255qE56m66LCD77yM5Lu35qC85Lmf5b6I5ZCI55CG44CC5pyN5Yqh5oCB5bqm5b6I5aW977yM5Lya5o6o6I2Q57uZ5pyL5Y+L77yB5pW05Liq6L+H56iL5b6I6aG65Yip77yM5LuO6aKE57qm5Yiw5a6M5oiQ57u05L+u5Y+q55So5LqGMuWwj+aXtuOAgicsXG4gICAgICBkYXRlOiAnMjAyNC0wMS0xMCdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAyLFxuICAgICAgbmFtZTogJ+eOi+WFiOeUnycsXG4gICAgICBhdmF0YXI6ICfwn5GoJyxcbiAgICAgIGxvY2F0aW9uOiAn5LiK5rW35rWm5Lic5Yy6JyxcbiAgICAgIHJhdGluZzogNSxcbiAgICAgIHNlcnZpY2U6ICfmuIXmtIHmnI3liqEnLFxuICAgICAgY29tbWVudDogJ+a4hea0gemYv+WnqOmdnuW4uOe7huW/g++8jOaKiuWutumHjOaJk+aJq+W+l+W5suW5suWHgOWHgO+8jOi/nueKhOinkuaXruaXr+mDveS4jeaUvui/h+OAgueUqOeahOa4hea0geeUqOWTgeS5n+W+iOeOr+S/ne+8jOayoeacieWIuua/gOaAp+awlOWRs+OAgicsXG4gICAgICBkYXRlOiAnMjAyNC0wMS0wOCdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAzLFxuICAgICAgbmFtZTogJ+W8oOWls+WjqycsXG4gICAgICBhdmF0YXI6ICfwn5Gp4oCN8J+SvCcsXG4gICAgICBsb2NhdGlvbjogJ+W5v+W3nuWkqeays+WMuicsXG4gICAgICByYXRpbmc6IDQsXG4gICAgICBzZXJ2aWNlOiAn576O5a65576O5Y+RJyxcbiAgICAgIGNvbW1lbnQ6ICfnkIblj5HluIjmioDmnK/lvojlpb3vvIzliarlh7rmnaXnmoTlj5Hlnovlvojmu6HmhI/jgILkuIrpl6jmnI3liqHlvojmlrnkvr/vvIzkuI3nlKjot5HnkIblj5HlupfmjpLpmJ/kuobjgILkuIvmrKHov5jkvJrnu6fnu63pooTnuqbjgIInLFxuICAgICAgZGF0ZTogJzIwMjQtMDEtMDUnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogNCxcbiAgICAgIG5hbWU6ICfpmYjlhYjnlJ8nLFxuICAgICAgYXZhdGFyOiAn8J+RqOKAjfCfkrsnLFxuICAgICAgbG9jYXRpb246ICfmt7HlnLPljZflsbHljLonLFxuICAgICAgcmF0aW5nOiA1LFxuICAgICAgc2VydmljZTogJ+WutuaVmeWfueiurScsXG4gICAgICBjb21tZW50OiAn6ICB5biI5b6I5pyJ6ICQ5b+D77yM5a2p5a2Q55qE5pWw5a2m5oiQ57up5pyJ5LqG5piO5pi+5o+Q6auY44CC5pWZ5a2m5pa55rOV5b6I5aW977yM6IO95aSf5Zug5p2Q5pa95pWZ44CC5a2p5a2Q546w5Zyo5a+55pWw5a2m5pu05pyJ5YW06Laj5LqG44CCJyxcbiAgICAgIGRhdGU6ICcyMDI0LTAxLTAzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IDUsXG4gICAgICBuYW1lOiAn5YiY5aWz5aOrJyxcbiAgICAgIGF2YXRhcjogJ/CfkanigI3wn6azJyxcbiAgICAgIGxvY2F0aW9uOiAn5oiQ6YO96ZSm5rGf5Yy6JyxcbiAgICAgIHJhdGluZzogNSxcbiAgICAgIHNlcnZpY2U6ICflrqDnianmnI3liqEnLFxuICAgICAgY29tbWVudDogJ+WuoOeJqeWvhOWFu+acjeWKoeW+iOS4k+S4mu+8jOeLl+eLl+WcqOmCo+mHjOW+iOW8gOW/g+OAguavj+WkqemDveS8muWPkeeFp+eJh+WSjOinhumike+8jOiuqeaIkeW+iOaUvuW/g+OAgueOr+Wig+W+iOWlve+8jOW3peS9nOS6uuWRmOS5n+W+iOi0n+i0o+OAgicsXG4gICAgICBkYXRlOiAnMjAyNC0wMS0wMSdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiA2LFxuICAgICAgbmFtZTogJ+i1teWFiOeUnycsXG4gICAgICBhdmF0YXI6ICfwn5Go4oCN8J+UpycsXG4gICAgICBsb2NhdGlvbjogJ+adreW3nuilv+a5luWMuicsXG4gICAgICByYXRpbmc6IDQsXG4gICAgICBzZXJ2aWNlOiAn5pCs5a625pyN5YqhJyxcbiAgICAgIGNvbW1lbnQ6ICfmkKzlrrbluIjlgoXlvojkuJPkuJrvvIzkuJzopb/ljIXoo4Xlvpflvojku5Tnu4bvvIzmsqHmnInmjZ/lnY/jgILmkKzov5Dov4fnqIvlvojlv6vvvIzmnI3liqHmgIHluqbkuZ/lvojlpb3jgILku7fmoLzpgI/mmI7vvIzmsqHmnInpop3lpJbmlLbotLnjgIInLFxuICAgICAgZGF0ZTogJzIwMjMtMTItMjgnXG4gICAgfVxuICBdXG5cbiAgY29uc3QgbmV4dFRlc3RpbW9uaWFsID0gKCkgPT4ge1xuICAgIHNldEN1cnJlbnRJbmRleCgocHJldkluZGV4KSA9PiBcbiAgICAgIHByZXZJbmRleCA9PT0gdGVzdGltb25pYWxzLmxlbmd0aCAtIDEgPyAwIDogcHJldkluZGV4ICsgMVxuICAgIClcbiAgfVxuXG4gIGNvbnN0IHByZXZUZXN0aW1vbmlhbCA9ICgpID0+IHtcbiAgICBzZXRDdXJyZW50SW5kZXgoKHByZXZJbmRleCkgPT4gXG4gICAgICBwcmV2SW5kZXggPT09IDAgPyB0ZXN0aW1vbmlhbHMubGVuZ3RoIC0gMSA6IHByZXZJbmRleCAtIDFcbiAgICApXG4gIH1cblxuICAvLyBBdXRvLXBsYXkgZnVuY3Rpb25hbGl0eVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwobmV4dFRlc3RpbW9uaWFsLCA1MDAwKVxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKVxuICB9LCBbXSlcblxuICBjb25zdCBnZXRWaXNpYmxlVGVzdGltb25pYWxzID0gKCkgPT4ge1xuICAgIGNvbnN0IHZpc2libGUgPSBbXVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgMzsgaSsrKSB7XG4gICAgICBjb25zdCBpbmRleCA9IChjdXJyZW50SW5kZXggKyBpKSAlIHRlc3RpbW9uaWFscy5sZW5ndGhcbiAgICAgIHZpc2libGUucHVzaCh0ZXN0aW1vbmlhbHNbaW5kZXhdKVxuICAgIH1cbiAgICByZXR1cm4gdmlzaWJsZVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNiBiZy1ncmF5LTUwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBtZDp0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICDnlKjmiLfor4Tku7dcbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAg5ZCs5ZCs55So5oi35oCO5LmI6K+077yM55yf5a6e6K+E5Lu36KeB6K+B5pyN5Yqh5ZOB6LSoXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRGVza3RvcCBWaWV3IC0gMyBjYXJkcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6YmxvY2tcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTYgbWItOFwiPlxuICAgICAgICAgICAge2dldFZpc2libGVUZXN0aW1vbmlhbHMoKS5tYXAoKHRlc3RpbW9uaWFsLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8Q2FyZCBrZXk9e3Rlc3RpbW9uaWFsLmlkfSBjbGFzc05hbWU9e2B0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtpbmRleCA9PT0gMSA/ICdzY2FsZS0xMDUgc2hhZG93LWxnJyA6ICdzY2FsZS05NSd9YH0+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLWJsdWUtNTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXdoaXRlIHRleHQtbGcgbXItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHt0ZXN0aW1vbmlhbC5hdmF0YXJ9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj57dGVzdGltb25pYWwubmFtZX08L2g0PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPnt0ZXN0aW1vbmlhbC5sb2NhdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggdGV4dC15ZWxsb3ctNDAwIG1yLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Wy4uLkFycmF5KDUpXS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTdGFyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaC00IHctNCAke2kgPCB0ZXN0aW1vbmlhbC5yYXRpbmcgPyAnZmlsbC1jdXJyZW50JyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e3Rlc3RpbW9uaWFsLnNlcnZpY2V9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgPFF1b3RlIGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMiAtbGVmdC0yIGgtNiB3LTYgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgaXRhbGljIHBsLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICBcInt0ZXN0aW1vbmlhbC5jb21tZW50fVwiXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC00XCI+XG4gICAgICAgICAgICAgICAgICAgIHt0ZXN0aW1vbmlhbC5kYXRlfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBNb2JpbGUgVmlldyAtIDEgY2FyZCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpoaWRkZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LW1kIG14LWF1dG8gbWItOFwiPlxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLWJsdWUtNTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXdoaXRlIHRleHQtbGcgbXItNFwiPlxuICAgICAgICAgICAgICAgICAgICB7dGVzdGltb25pYWxzW2N1cnJlbnRJbmRleF0uYXZhdGFyfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+e3Rlc3RpbW9uaWFsc1tjdXJyZW50SW5kZXhdLm5hbWV9PC9oND5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e3Rlc3RpbW9uaWFsc1tjdXJyZW50SW5kZXhdLmxvY2F0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHRleHQteWVsbG93LTQwMCBtci0yXCI+XG4gICAgICAgICAgICAgICAgICAgIHtbLi4uQXJyYXkoNSldLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxTdGFyXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2l9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLTQgdy00ICR7aSA8IHRlc3RpbW9uaWFsc1tjdXJyZW50SW5kZXhdLnJhdGluZyA/ICdmaWxsLWN1cnJlbnQnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e3Rlc3RpbW9uaWFsc1tjdXJyZW50SW5kZXhdLnNlcnZpY2V9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgIDxRdW90ZSBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTIgLWxlZnQtMiBoLTYgdy02IHRleHQtZ3JheS0zMDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBpdGFsaWMgcGwtNFwiPlxuICAgICAgICAgICAgICAgICAgICBcInt0ZXN0aW1vbmlhbHNbY3VycmVudEluZGV4XS5jb21tZW50fVwiXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtNFwiPlxuICAgICAgICAgICAgICAgICAge3Rlc3RpbW9uaWFsc1tjdXJyZW50SW5kZXhdLmRhdGV9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBOYXZpZ2F0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9e3ByZXZUZXN0aW1vbmlhbH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQtZnVsbFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPENoZXZyb25MZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIHt0ZXN0aW1vbmlhbHMubWFwKChfLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDdXJyZW50SW5kZXgoaW5kZXgpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctMiBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICBpbmRleCA9PT0gY3VycmVudEluZGV4ID8gJ2JnLXByaW1hcnknIDogJ2JnLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgc2l6ZT1cImljb25cIlxuICAgICAgICAgICAgb25DbGljaz17bmV4dFRlc3RpbW9uaWFsfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiU3RhciIsIkNoZXZyb25MZWZ0IiwiQ2hldnJvblJpZ2h0IiwiUXVvdGUiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJUZXN0aW1vbmlhbHNTZWN0aW9uIiwiY3VycmVudEluZGV4Iiwic2V0Q3VycmVudEluZGV4IiwidGVzdGltb25pYWxzIiwiaWQiLCJuYW1lIiwiYXZhdGFyIiwibG9jYXRpb24iLCJyYXRpbmciLCJzZXJ2aWNlIiwiY29tbWVudCIsImRhdGUiLCJuZXh0VGVzdGltb25pYWwiLCJwcmV2SW5kZXgiLCJsZW5ndGgiLCJwcmV2VGVzdGltb25pYWwiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsImdldFZpc2libGVUZXN0aW1vbmlhbHMiLCJ2aXNpYmxlIiwiaSIsImluZGV4IiwicHVzaCIsInNlY3Rpb24iLCJjbGFzc05hbWUiLCJkaXYiLCJoMiIsInAiLCJtYXAiLCJ0ZXN0aW1vbmlhbCIsImg0IiwiQXJyYXkiLCJfIiwic3BhbiIsInZhcmlhbnQiLCJzaXplIiwib25DbGljayIsImJ1dHRvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/home/<USER>"));

/***/ })

});