/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"b926de02a962\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzP2RlM2EiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiOTI2ZGUwMmE5NjJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createService: function() { return /* binding */ createService; },\n/* harmony export */   deleteService: function() { return /* binding */ deleteService; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   getServiceById: function() { return /* binding */ getServiceById; },\n/* harmony export */   getServices: function() { return /* binding */ getServices; },\n/* harmony export */   resetPassword: function() { return /* binding */ resetPassword; },\n/* harmony export */   signInWithEmail: function() { return /* binding */ signInWithEmail; },\n/* harmony export */   signOut: function() { return /* binding */ signOut; },\n/* harmony export */   signUpWithEmail: function() { return /* binding */ signUpWithEmail; },\n/* harmony export */   supabase: function() { return /* binding */ supabase; },\n/* harmony export */   updateService: function() { return /* binding */ updateService; }\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://placeholder.supabase.co\";\nconst supabaseAnonKey = \"placeholder_key\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// 辅助函数\nasync function getCurrentUser() {\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) throw error;\n    return user;\n}\nasync function signOut() {\n    const { error } = await supabase.auth.signOut();\n    if (error) throw error;\n}\nasync function signInWithEmail(email, password) {\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    if (error) throw error;\n    return data;\n}\nasync function signUpWithEmail(email, password, metadata) {\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: metadata\n        }\n    });\n    if (error) throw error;\n    return data;\n}\nasync function resetPassword(email) {\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n    });\n    if (error) throw error;\n}\n// 服务相关函数\nasync function getServices(filters) {\n    let query = supabase.from(\"services\").select(\"\\n      *,\\n      provider:profiles(*)\\n    \").eq(\"status\", \"active\").order(\"created_at\", {\n        ascending: false\n    });\n    if (filters === null || filters === void 0 ? void 0 : filters.category) {\n        query = query.eq(\"category\", filters.category);\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.location) {\n        query = query.ilike(\"location\", \"%\".concat(filters.location, \"%\"));\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.priceMin) {\n        query = query.gte(\"price\", filters.priceMin);\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.priceMax) {\n        query = query.lte(\"price\", filters.priceMax);\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.search) {\n        query = query.or(\"title.ilike.%\".concat(filters.search, \"%,description.ilike.%\").concat(filters.search, \"%\"));\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.limit) {\n        query = query.limit(filters.limit);\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.offset) {\n        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);\n    }\n    const { data, error } = await query;\n    if (error) throw error;\n    return data;\n}\nasync function getServiceById(id) {\n    const { data, error } = await supabase.from(\"services\").select(\"\\n      *,\\n      provider:profiles(*),\\n      reviews(*, user:profiles(*))\\n    \").eq(\"id\", id).single();\n    if (error) throw error;\n    return data;\n}\nasync function createService(service) {\n    const { data, error } = await supabase.from(\"services\").insert(service).select().single();\n    if (error) throw error;\n    return data;\n}\nasync function updateService(id, updates) {\n    const { data, error } = await supabase.from(\"services\").update(updates).eq(\"id\", id).select().single();\n    if (error) throw error;\n    return data;\n}\nasync function deleteService(id) {\n    const { error } = await supabase.from(\"services\").delete().eq(\"id\", id);\n    if (error) throw error;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/supabase.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \********************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\"};\n    if(true) {\n      // 1749832719840\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwL2xheW91dC50c3hcIixcImltcG9ydFwiOlwiSW50ZXJcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiaW50ZXJcIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyxnRkFBZ0Y7QUFDM0csT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQStILGNBQWMsc0RBQXNEO0FBQ2pPLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZm9udC9nb29nbGUvdGFyZ2V0LmNzcz81YmZiIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidfX0ludGVyX2U4Y2UwYycsICdfX0ludGVyX0ZhbGxiYWNrX2U4Y2UwYydcIixcImZvbnRTdHlsZVwiOlwibm9ybWFsXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV9lOGNlMGNcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc0OTgzMjcxOTg0MFxuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCIvVXNlcnMvaGVudGVyL0FJL2VpYW4vYXBwL0xvY2FsU2VydmljZVdlYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}\n"));

/***/ })

});