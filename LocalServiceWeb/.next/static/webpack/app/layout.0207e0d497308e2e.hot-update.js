"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createService: function() { return /* binding */ createService; },\n/* harmony export */   deleteService: function() { return /* binding */ deleteService; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   getServiceById: function() { return /* binding */ getServiceById; },\n/* harmony export */   getServices: function() { return /* binding */ getServices; },\n/* harmony export */   resetPassword: function() { return /* binding */ resetPassword; },\n/* harmony export */   signInWithEmail: function() { return /* binding */ signInWithEmail; },\n/* harmony export */   signOut: function() { return /* binding */ signOut; },\n/* harmony export */   signUpWithEmail: function() { return /* binding */ signUpWithEmail; },\n/* harmony export */   supabase: function() { return /* binding */ supabase; },\n/* harmony export */   updateService: function() { return /* binding */ updateService; }\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// 辅助函数\nasync function getCurrentUser() {\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) throw error;\n    return user;\n}\nasync function signOut() {\n    const { error } = await supabase.auth.signOut();\n    if (error) throw error;\n}\nasync function signInWithEmail(email, password) {\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    if (error) throw error;\n    return data;\n}\nasync function signUpWithEmail(email, password, metadata) {\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: metadata\n        }\n    });\n    if (error) throw error;\n    return data;\n}\nasync function resetPassword(email) {\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n    });\n    if (error) throw error;\n}\n// 服务相关函数\nasync function getServices(filters) {\n    let query = supabase.from(\"services\").select(\"\\n      *,\\n      provider:profiles(*)\\n    \").eq(\"status\", \"active\").order(\"created_at\", {\n        ascending: false\n    });\n    if (filters === null || filters === void 0 ? void 0 : filters.category) {\n        query = query.eq(\"category\", filters.category);\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.location) {\n        query = query.ilike(\"location\", \"%\".concat(filters.location, \"%\"));\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.priceMin) {\n        query = query.gte(\"price\", filters.priceMin);\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.priceMax) {\n        query = query.lte(\"price\", filters.priceMax);\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.search) {\n        query = query.or(\"title.ilike.%\".concat(filters.search, \"%,description.ilike.%\").concat(filters.search, \"%\"));\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.limit) {\n        query = query.limit(filters.limit);\n    }\n    if (filters === null || filters === void 0 ? void 0 : filters.offset) {\n        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);\n    }\n    const { data, error } = await query;\n    if (error) throw error;\n    return data;\n}\nasync function getServiceById(id) {\n    const { data, error } = await supabase.from(\"services\").select(\"\\n      *,\\n      provider:profiles(*),\\n      reviews(*, user:profiles(*))\\n    \").eq(\"id\", id).single();\n    if (error) throw error;\n    return data;\n}\nasync function createService(service) {\n    const { data, error } = await supabase.from(\"services\").insert(service).select().single();\n    if (error) throw error;\n    return data;\n}\nasync function updateService(id, updates) {\n    const { data, error } = await supabase.from(\"services\").update(updates).eq(\"id\", id).select().single();\n    if (error) throw error;\n    return data;\n}\nasync function deleteService(id) {\n    const { error } = await supabase.from(\"services\").delete().eq(\"id\", id);\n    if (error) throw error;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/supabase.ts\n"));

/***/ })

});