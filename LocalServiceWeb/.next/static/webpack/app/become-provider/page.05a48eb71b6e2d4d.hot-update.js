"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/become-provider/page",{

/***/ "(app-pages-browser)/./app/become-provider/page.tsx":
/*!**************************************!*\
  !*** ./app/become-provider/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BecomeProviderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,Shield,Star,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,Shield,Star,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,Shield,Star,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,Shield,Star,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,Shield,Star,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,Shield,Star,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction BecomeProviderPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        category: \"\",\n        experience: \"\",\n        description: \"\"\n    });\n    const benefits = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                lineNumber: 22,\n                columnNumber: 13\n            }, this),\n            title: \"海量客户\",\n            description: \"接触数万潜在客户，扩大业务范围\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                lineNumber: 27,\n                columnNumber: 13\n            }, this),\n            title: \"增加收入\",\n            description: \"平均服务商月收入提升40%\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                lineNumber: 32,\n                columnNumber: 13\n            }, this),\n            title: \"平台保障\",\n            description: \"完善的保险体系，保障您的权益\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 13\n            }, this),\n            title: \"灵活时间\",\n            description: \"自由安排工作时间，工作生活平衡\"\n        }\n    ];\n    const steps = [\n        {\n            step: 1,\n            title: \"提交申请\",\n            description: \"填写基本信息和服务类别\"\n        },\n        {\n            step: 2,\n            title: \"资质审核\",\n            description: \"上传相关证件和资质证明\"\n        },\n        {\n            step: 3,\n            title: \"技能认证\",\n            description: \"通过平台技能测试或面试\"\n        },\n        {\n            step: 4,\n            title: \"开始接单\",\n            description: \"审核通过后即可开始接单赚钱\"\n        }\n    ];\n    const categories = [\n        \"家庭维修\",\n        \"清洁服务\",\n        \"美容美发\",\n        \"家教培训\",\n        \"宠物服务\",\n        \"搬家服务\",\n        \"摄影摄像\",\n        \"健身运动\"\n    ];\n    const successStories = [\n        {\n            name: \"张师傅\",\n            category: \"家电维修\",\n            income: \"月收入 \\xa58,000+\",\n            rating: 4.9,\n            orders: 156,\n            story: \"加入平台2年，从兼职到全职，现在每月稳定收入8000+，客户都很满意。\"\n        },\n        {\n            name: \"李阿姨\",\n            category: \"清洁服务\",\n            income: \"月收入 \\xa56,500+\",\n            rating: 4.8,\n            orders: 203,\n            story: \"通过平台找到了稳定的客源，工作时间灵活，收入比以前提高了很多。\"\n        }\n    ];\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        console.log(\"Provider application:\", formData);\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold mb-6\",\n                            children: \"成为服务商\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto\",\n                            children: \"加入我们的平台，开启您的创业之路。数万客户等待您的专业服务\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    className: \"bg-orange-500 hover:bg-orange-600 text-white px-8\",\n                                    children: \"立即申请\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"outline\",\n                                    className: \"border-white text-blue-500 hover:bg-white hover:text-blue-600\",\n                                    children: \"了解更多\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                        children: \"为什么选择我们\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600\",\n                                        children: \"专业平台，助您事业腾飞\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                children: benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"text-center hover:shadow-lg transition-shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-primary mb-4 flex justify-center\",\n                                                    children: benefit.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2\",\n                                                    children: benefit.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: benefit.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                        children: \"申请流程\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600\",\n                                        children: \"简单4步，快速入驻\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4\",\n                                                children: step.step\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: step.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"hidden md:block absolute top-8 -right-4 h-6 w-6 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                children: \"立即申请成为服务商\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleSubmit,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"姓名 *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                name: \"name\",\n                                                                required: true,\n                                                                placeholder: \"请输入您的姓名\",\n                                                                value: formData.name,\n                                                                onChange: handleInputChange\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"邮箱 *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                name: \"email\",\n                                                                type: \"email\",\n                                                                required: true,\n                                                                placeholder: \"请输入邮箱地址\",\n                                                                value: formData.email,\n                                                                onChange: handleInputChange\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"手机号 *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                name: \"phone\",\n                                                                type: \"tel\",\n                                                                required: true,\n                                                                placeholder: \"请输入手机号码\",\n                                                                value: formData.phone,\n                                                                onChange: handleInputChange\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"服务类别 *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                name: \"category\",\n                                                                required: true,\n                                                                value: formData.category,\n                                                                onChange: handleInputChange,\n                                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"请选择服务类别\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: category,\n                                                                            children: category\n                                                                        }, category, false, {\n                                                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                            lineNumber: 231,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"从业经验 *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                name: \"experience\",\n                                                                required: true,\n                                                                value: formData.experience,\n                                                                onChange: handleInputChange,\n                                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"请选择从业经验\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                        lineNumber: 247,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"1年以下\",\n                                                                        children: \"1年以下\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"1-3年\",\n                                                                        children: \"1-3年\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"3-5年\",\n                                                                        children: \"3-5年\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"5年以上\",\n                                                                        children: \"5年以上\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"服务描述\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                name: \"description\",\n                                                                rows: 4,\n                                                                placeholder: \"请简单描述您的服务内容和优势\",\n                                                                value: formData.description,\n                                                                onChange: handleInputChange,\n                                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"submit\",\n                                                        className: \"w-full\",\n                                                        size: \"lg\",\n                                                        children: \"提交申请\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                        children: \"成功案例\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: successStories.map((story, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl font-bold\",\n                                                                children: story.name.charAt(0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-semibold\",\n                                                                                children: story.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                children: story.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                                lineNumber: 291,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4 text-sm text-gray-600 mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-green-600\",\n                                                                                children: story.income\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                                lineNumber: 294,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_Shield_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                        className: \"h-4 w-4 text-yellow-400 fill-current mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                                        lineNumber: 296,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    story.rating\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                                lineNumber: 295,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    story.orders,\n                                                                                    \"单\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-700 italic\",\n                                                                        children: [\n                                                                            '\"',\n                                                                            story.story,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                                children: \"常见问题\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white rounded-lg p-4 border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-2\",\n                                                                children: \"申请需要什么条件？\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm\",\n                                                                children: \"需要有相关技能和经验，通过平台审核即可。\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white rounded-lg p-4 border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-2\",\n                                                                children: \"平台收取多少佣金？\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm\",\n                                                                children: \"平台收取订单金额的10%作为服务费。\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white rounded-lg p-4 border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-2\",\n                                                                children: \"多久可以开始接单？\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm\",\n                                                                children: \"审核通过后即可开始接单，通常3-5个工作日。\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/AI/eian/app/LocalServiceWeb/app/become-provider/page.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n_s(BecomeProviderPage, \"+/rzDcVIHefn6z7lHdNzzz10/Ow=\");\n_c = BecomeProviderPage;\nvar _c;\n$RefreshReg$(_c, \"BecomeProviderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/become-provider/page.tsx\n"));

/***/ })

});