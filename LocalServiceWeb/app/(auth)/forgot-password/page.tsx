'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    // 模拟API调用
    setTimeout(() => {
      setIsLoading(false)
      setIsSubmitted(true)
    }, 2000)
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <Link href="/" className="flex items-center justify-center space-x-2 mb-6">
              <div className="h-10 w-10 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-white font-bold text-lg">本</span>
              </div>
              <span className="font-bold text-2xl text-gray-900">本地生活服务</span>
            </Link>
          </div>

          <Card>
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">邮件已发送</h2>
              <p className="text-gray-600 mb-6">
                我们已向 <span className="font-medium">{email}</span> 发送了密码重置链接。
                请检查您的邮箱并点击链接重置密码。
              </p>
              <div className="space-y-3">
                <Button asChild className="w-full">
                  <Link href="/auth/login">返回登录</Link>
                </Button>
                <Button variant="outline" className="w-full" onClick={() => setIsSubmitted(false)}>
                  重新发送邮件
                </Button>
              </div>
              <p className="text-sm text-gray-500 mt-4">
                没有收到邮件？请检查垃圾邮件文件夹，或者
                <button 
                  onClick={() => setIsSubmitted(false)}
                  className="text-primary hover:text-primary/80 ml-1"
                >
                  重新输入邮箱地址
                </button>
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Logo和标题 */}
        <div className="text-center">
          <Link href="/" className="flex items-center justify-center space-x-2 mb-6">
            <div className="h-10 w-10 rounded-lg bg-primary flex items-center justify-center">
              <span className="text-white font-bold text-lg">本</span>
            </div>
            <span className="font-bold text-2xl text-gray-900">本地生活服务</span>
          </Link>
          <h2 className="text-3xl font-bold text-gray-900">
            忘记密码
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            输入您的邮箱地址，我们将发送密码重置链接
          </p>
        </div>

        {/* 重置密码表单 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center">重置密码</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* 邮箱输入 */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱地址
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    placeholder="请输入您的邮箱地址"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  请输入您注册时使用的邮箱地址
                </p>
              </div>

              {/* 发送按钮 */}
              <Button 
                type="submit" 
                className="w-full" 
                size="lg"
                disabled={isLoading || !email}
              >
                {isLoading ? '发送中...' : '发送重置链接'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* 返回登录 */}
        <div className="text-center">
          <Link 
            href="/auth/login" 
            className="inline-flex items-center text-sm text-primary hover:text-primary/80"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            返回登录页面
          </Link>
        </div>

        {/* 其他帮助 */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            还是无法登录？{' '}
            <Link href="/contact" className="font-medium text-primary hover:text-primary/80">
              联系客服
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
