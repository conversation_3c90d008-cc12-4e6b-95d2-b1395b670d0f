'use client'

import { useState } from 'react'
import { Phone, Mail, MapPin, Clock, Send } from 'lucide-react'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })

  const contactInfo = [
    {
      icon: <Phone className="h-6 w-6" />,
      title: '客服热线',
      content: '************',
      description: '7×24小时为您服务'
    },
    {
      icon: <Mail className="h-6 w-6" />,
      title: '邮箱地址',
      content: '<EMAIL>',
      description: '我们会在24小时内回复'
    },
    {
      icon: <MapPin className="h-6 w-6" />,
      title: '公司地址',
      content: '北京市朝阳区xxx大厦',
      description: '欢迎预约参观'
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: '工作时间',
      content: '周一至周日 8:00-20:00',
      description: '节假日正常服务'
    }
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Contact form submitted:', formData)
    // 这里处理表单提交逻辑
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">联系我们</h1>
          <p className="text-xl text-blue-100 max-w-2xl mx-auto">
            有任何问题或建议，我们都很乐意为您解答
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* 联系信息 */}
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-8">联系方式</h2>
            
            <div className="space-y-6 mb-8">
              {contactInfo.map((info, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="text-primary">
                        {info.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-1">{info.title}</h3>
                        <p className="text-gray-900 font-medium mb-1">{info.content}</p>
                        <p className="text-gray-600 text-sm">{info.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* 地图区域 */}
            <Card>
              <CardHeader>
                <CardTitle>公司位置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <MapPin className="h-12 w-12 mx-auto mb-2" />
                    <p>地图加载中...</p>
                    <p className="text-sm">北京市朝阳区xxx大厦</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 联系表单 */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>发送消息</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        姓名 *
                      </label>
                      <Input
                        name="name"
                        required
                        placeholder="请输入您的姓名"
                        value={formData.name}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        邮箱 *
                      </label>
                      <Input
                        name="email"
                        type="email"
                        required
                        placeholder="请输入邮箱地址"
                        value={formData.email}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        手机号
                      </label>
                      <Input
                        name="phone"
                        type="tel"
                        placeholder="请输入手机号码"
                        value={formData.phone}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        主题 *
                      </label>
                      <select
                        name="subject"
                        required
                        value={formData.subject}
                        onChange={handleInputChange}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        <option value="">请选择主题</option>
                        <option value="general">一般咨询</option>
                        <option value="service">服务问题</option>
                        <option value="technical">技术支持</option>
                        <option value="business">商务合作</option>
                        <option value="complaint">投诉建议</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      消息内容 *
                    </label>
                    <textarea
                      name="message"
                      required
                      rows={6}
                      placeholder="请详细描述您的问题或建议..."
                      value={formData.message}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>

                  <Button type="submit" className="w-full" size="lg">
                    <Send className="h-4 w-4 mr-2" />
                    发送消息
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* 常见问题 */}
            <Card className="mt-8">
              <CardHeader>
                <CardTitle>常见问题</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">如何注册成为服务商？</h4>
                    <p className="text-gray-600 text-sm">
                      您可以点击"成为服务商"按钮，填写申请表单，我们会在3-5个工作日内审核。
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">服务出现问题如何处理？</h4>
                    <p className="text-gray-600 text-sm">
                      请及时联系客服，我们会协调处理并确保您的权益得到保障。
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">如何申请退款？</h4>
                    <p className="text-gray-600 text-sm">
                      在订单页面点击申请退款，或联系客服处理，符合条件的订单会及时退款。
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
