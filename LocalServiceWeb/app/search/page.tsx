import { Suspense } from 'react'
import { Search, Filter, MapPin, Star, Grid, List, Clock } from 'lucide-react'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Card, CardContent } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'

interface SearchPageProps {
  searchParams: {
    q?: string
    location?: string
    category?: string
  }
}

export default function SearchPage({ searchParams }: SearchPageProps) {
  const query = searchParams.q || ''
  const location = searchParams.location || ''
  const category = searchParams.category || ''

  // 模拟搜索结果
  const searchResults = [
    {
      id: '1',
      title: '专业家电维修服务',
      description: '提供各类家电维修服务，包括空调、洗衣机、冰箱等。拥有10年维修经验，技术过硬，价格合理。',
      price: 80,
      priceUnit: '小时',
      rating: 4.8,
      reviewCount: 156,
      category: '家庭维修',
      location: '朝阳区',
      distance: '2.3km',
      responseTime: '30分钟内',
      tags: ['上门服务', '质量保证', '价格透明'],
      provider: {
        name: '张师傅',
        rating: 4.9,
        isVerified: true,
      },
      isOnline: true
    },
    {
      id: '2',
      title: '深度清洁服务',
      description: '专业清洁团队，提供家庭、办公室深度清洁服务。使用环保清洁用品，细致入微。',
      price: 120,
      priceUnit: '次',
      rating: 4.9,
      reviewCount: 203,
      category: '清洁服务',
      location: '海淀区',
      distance: '5.1km',
      responseTime: '1小时内',
      tags: ['环保用品', '专业团队', '细致服务'],
      provider: {
        name: '李阿姨',
        rating: 4.8,
        isVerified: true,
      },
      isOnline: false
    },
    {
      id: '3',
      title: '上门理发服务',
      description: '专业理发师上门服务，方便快捷。提供男女老少各种发型设计，工具齐全。',
      price: 60,
      priceUnit: '次',
      rating: 4.7,
      reviewCount: 89,
      category: '美容美发',
      location: '西城区',
      distance: '3.7km',
      responseTime: '2小时内',
      tags: ['上门服务', '专业技术', '工具齐全'],
      provider: {
        name: '王师傅',
        rating: 4.6,
        isVerified: false,
      },
      isOnline: true
    }
  ]

  const relatedSearches = [
    '家电维修', '空调维修', '洗衣机维修', '清洁服务', '家庭保洁', '上门理发'
  ]

  const popularCategories = [
    '家庭维修', '清洁服务', '美容美发', '家教培训', '宠物服务', '搬家服务'
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 搜索结果标题 */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {query ? `"${query}"的搜索结果` : '搜索结果'}
          </h1>
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            {location && <span>位置：{location}</span>}
            {category && <span>分类：{category}</span>}
            <span>找到 {searchResults.length} 个结果</span>
          </div>
        </div>

        {/* 搜索和筛选栏 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* 搜索框 */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="search"
                placeholder="搜索服务..."
                defaultValue={query}
                className="pl-10"
              />
            </div>
            
            {/* 位置筛选 */}
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="选择位置..."
                defaultValue={location}
                className="pl-10 w-full lg:w-48"
              />
            </div>
            
            {/* 筛选按钮 */}
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              筛选
            </Button>
            
            {/* 视图切换 */}
            <div className="flex border rounded-lg">
              <Button variant="ghost" size="sm" className="rounded-r-none">
                <Grid className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="rounded-l-none">
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* 侧边栏 */}
          <div className="lg:w-64">
            <div className="space-y-6">
              {/* 热门分类 */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="font-semibold text-lg mb-4">热门分类</h3>
                <div className="space-y-2">
                  {popularCategories.map((cat) => (
                    <button
                      key={cat}
                      className="block w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors text-sm"
                    >
                      {cat}
                    </button>
                  ))}
                </div>
              </div>

              {/* 相关搜索 */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="font-semibold text-lg mb-4">相关搜索</h3>
                <div className="flex flex-wrap gap-2">
                  {relatedSearches.map((search) => (
                    <button
                      key={search}
                      className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm transition-colors"
                    >
                      {search}
                    </button>
                  ))}
                </div>
              </div>

              {/* 筛选选项 */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="font-semibold text-lg mb-4">筛选条件</h3>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">价格范围</h4>
                    <div className="space-y-2">
                      {['¥0 - ¥50', '¥50 - ¥100', '¥100 - ¥200', '¥200+'].map((range) => (
                        <label key={range} className="flex items-center">
                          <input type="checkbox" className="mr-2" />
                          <span className="text-sm">{range}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">评分</h4>
                    <div className="space-y-2">
                      {[5, 4, 3].map((rating) => (
                        <label key={rating} className="flex items-center">
                          <input type="checkbox" className="mr-2" />
                          <div className="flex items-center">
                            {[...Array(rating)].map((_, i) => (
                              <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                            ))}
                            <span className="text-sm ml-1">及以上</span>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">服务特色</h4>
                    <div className="space-y-2">
                      {['上门服务', '24小时服务', '认证服务商', '质量保证'].map((feature) => (
                        <label key={feature} className="flex items-center">
                          <input type="checkbox" className="mr-2" />
                          <span className="text-sm">{feature}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 搜索结果 */}
          <div className="flex-1">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center space-x-4">
                <span className="text-gray-600">排序：</span>
                <select className="border rounded-md px-3 py-2">
                  <option>相关度</option>
                  <option>距离最近</option>
                  <option>价格从低到高</option>
                  <option>价格从高到低</option>
                  <option>评分最高</option>
                  <option>最新发布</option>
                </select>
              </div>
            </div>

            <div className="space-y-6">
              {searchResults.map((service) => (
                <Card key={service.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-6">
                      {/* 服务图片 */}
                      <div className="w-full md:w-48 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-4xl opacity-50">📷</span>
                      </div>
                      
                      {/* 服务信息 */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                              {service.title}
                            </h3>
                            <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                              <div className="flex items-center">
                                <Star className="w-4 h-4 text-yellow-400 mr-1" />
                                {service.rating} ({service.reviewCount}评价)
                              </div>
                              <div className="flex items-center">
                                <MapPin className="w-4 h-4 mr-1" />
                                {service.location} · {service.distance}
                              </div>
                              <div className="flex items-center">
                                <Clock className="w-4 h-4 mr-1" />
                                {service.responseTime}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-primary">
                              ¥{service.price}
                            </div>
                            <div className="text-sm text-gray-500">
                              /{service.priceUnit}
                            </div>
                          </div>
                        </div>
                        
                        <p className="text-gray-700 mb-4 line-clamp-2">
                          {service.description}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm mr-2">
                                {service.provider.name.charAt(0)}
                              </div>
                              <span className="text-sm font-medium">{service.provider.name}</span>
                              {service.isOnline && (
                                <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                              )}
                            </div>
                            
                            <div className="flex flex-wrap gap-2">
                              <Badge>{service.category}</Badge>
                              {service.tags.slice(0, 2).map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              收藏
                            </Button>
                            <Button size="sm">
                              查看详情
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            {/* 分页 */}
            <div className="flex justify-center mt-8">
              <div className="flex space-x-2">
                <Button variant="outline" disabled>上一页</Button>
                <Button variant="outline" className="bg-primary text-white">1</Button>
                <Button variant="outline">2</Button>
                <Button variant="outline">3</Button>
                <Button variant="outline">下一页</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
