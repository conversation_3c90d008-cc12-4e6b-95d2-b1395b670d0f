'use client'

import { useState } from 'react'
import { MapPin, Clock, Users, Briefcase, Heart, Star, Send } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'

export default function CareersPage() {
  const [selectedJob, setSelectedJob] = useState<string | null>(null)

  const benefits = [
    {
      icon: <Heart className="h-8 w-8" />,
      title: '完善福利',
      description: '五险一金、年终奖金、带薪年假、节日福利'
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: '团队氛围',
      description: '年轻活力的团队，开放包容的工作环境'
    },
    {
      icon: <Star className="h-8 w-8" />,
      title: '成长机会',
      description: '完善的培训体系，广阔的职业发展空间'
    },
    {
      icon: <Briefcase className="h-8 w-8" />,
      title: '弹性工作',
      description: '灵活的工作时间，支持远程办公'
    }
  ]

  const jobs = [
    {
      id: '1',
      title: '前端开发工程师',
      department: '技术部',
      location: '北京',
      type: '全职',
      experience: '3-5年',
      salary: '20K-35K',
      description: '负责平台前端页面开发，与设计师、后端工程师协作完成产品功能',
      requirements: [
        '熟练掌握React、Vue等前端框架',
        '熟悉HTML5、CSS3、JavaScript',
        '有移动端开发经验优先',
        '良好的代码规范和团队协作能力'
      ],
      responsibilities: [
        '负责Web前端页面开发和维护',
        '与UI设计师协作，实现设计稿',
        '优化前端性能，提升用户体验',
        '参与产品需求讨论和技术方案设计'
      ]
    },
    {
      id: '2',
      title: '后端开发工程师',
      department: '技术部',
      location: '北京',
      type: '全职',
      experience: '3-5年',
      salary: '25K-40K',
      description: '负责平台后端服务开发，设计和实现高性能、高可用的系统架构',
      requirements: [
        '熟练掌握Java、Python或Go语言',
        '熟悉Spring Boot、Django等框架',
        '有分布式系统开发经验',
        '熟悉MySQL、Redis等数据库'
      ],
      responsibilities: [
        '负责后端API接口开发',
        '设计和优化数据库结构',
        '参与系统架构设计',
        '解决系统性能和稳定性问题'
      ]
    },
    {
      id: '3',
      title: '产品经理',
      department: '产品部',
      location: '北京',
      type: '全职',
      experience: '3-5年',
      salary: '25K-40K',
      description: '负责产品规划和设计，推动产品功能迭代和用户体验优化',
      requirements: [
        '3年以上互联网产品经验',
        '熟悉用户研究和数据分析',
        '有O2O或生活服务类产品经验优先',
        '优秀的沟通协调能力'
      ],
      responsibilities: [
        '制定产品发展规划和路线图',
        '收集和分析用户需求',
        '设计产品功能和交互流程',
        '跟进产品开发进度和质量'
      ]
    },
    {
      id: '4',
      title: 'UI/UX设计师',
      department: '设计部',
      location: '北京',
      type: '全职',
      experience: '2-4年',
      salary: '18K-30K',
      description: '负责产品界面设计和用户体验优化，打造优秀的视觉效果',
      requirements: [
        '熟练使用Figma、Sketch等设计工具',
        '有移动端和Web端设计经验',
        '良好的视觉设计和交互设计能力',
        '关注用户体验和设计趋势'
      ],
      responsibilities: [
        '负责产品界面设计',
        '制定设计规范和组件库',
        '参与用户研究和可用性测试',
        '与开发团队协作确保设计落地'
      ]
    },
    {
      id: '5',
      title: '运营专员',
      department: '运营部',
      location: '北京',
      type: '全职',
      experience: '1-3年',
      salary: '12K-20K',
      description: '负责平台运营活动策划和执行，提升用户活跃度和留存率',
      requirements: [
        '有互联网运营经验',
        '熟悉社交媒体和内容营销',
        '数据分析能力强',
        '创意思维和执行力强'
      ],
      responsibilities: [
        '策划和执行运营活动',
        '管理社交媒体账号',
        '分析运营数据并优化策略',
        '维护用户社群和客户关系'
      ]
    }
  ]

  const [applicationForm, setApplicationForm] = useState({
    name: '',
    email: '',
    phone: '',
    position: '',
    experience: '',
    resume: null
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setApplicationForm(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Application submitted:', applicationForm)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 英雄区域 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">加入我们</h1>
          <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
            与我们一起改变生活服务行业，创造更美好的未来
          </p>
          <Button size="lg" className="bg-orange-500 hover:bg-orange-600 text-white px-8">
            查看职位
          </Button>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        {/* 公司文化 */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">为什么选择我们</h2>
            <p className="text-lg text-gray-600">在这里，您将获得不仅仅是一份工作</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="text-primary mb-4 flex justify-center">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 职位列表 */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">开放职位</h2>
            <p className="text-lg text-gray-600">找到适合您的职位，开启职业新篇章</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 职位列表 */}
            <div className="space-y-6">
              {jobs.map((job) => (
                <Card 
                  key={job.id} 
                  className={`cursor-pointer transition-all ${
                    selectedJob === job.id ? 'ring-2 ring-primary shadow-lg' : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedJob(selectedJob === job.id ? null : job.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">{job.title}</h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span className="flex items-center">
                            <Briefcase className="h-4 w-4 mr-1" />
                            {job.department}
                          </span>
                          <span className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {job.location}
                          </span>
                          <span className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {job.experience}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-primary">{job.salary}</div>
                        <Badge variant="outline">{job.type}</Badge>
                      </div>
                    </div>
                    <p className="text-gray-700 mb-4">{job.description}</p>
                    <Button 
                      variant={selectedJob === job.id ? "default" : "outline"} 
                      size="sm"
                    >
                      {selectedJob === job.id ? '收起详情' : '查看详情'}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* 职位详情 */}
            <div className="sticky top-8">
              {selectedJob ? (
                <Card>
                  <CardHeader>
                    <CardTitle>
                      {jobs.find(job => job.id === selectedJob)?.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const job = jobs.find(job => job.id === selectedJob)
                      if (!job) return null
                      
                      return (
                        <div className="space-y-6">
                          <div>
                            <h4 className="font-semibold mb-3">职位要求</h4>
                            <ul className="space-y-2">
                              {job.requirements.map((req, index) => (
                                <li key={index} className="flex items-start">
                                  <div className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                  <span className="text-gray-700">{req}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                          
                          <div>
                            <h4 className="font-semibold mb-3">工作职责</h4>
                            <ul className="space-y-2">
                              {job.responsibilities.map((resp, index) => (
                                <li key={index} className="flex items-start">
                                  <div className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                  <span className="text-gray-700">{resp}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                          
                          <Button className="w-full">
                            申请此职位
                          </Button>
                        </div>
                      )
                    })()}
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">选择职位</h3>
                    <p className="text-gray-600">点击左侧职位查看详细信息</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </section>

        {/* 简历投递 */}
        <section>
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center">投递简历</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      姓名 *
                    </label>
                    <Input
                      name="name"
                      required
                      placeholder="请输入您的姓名"
                      value={applicationForm.name}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱 *
                    </label>
                    <Input
                      name="email"
                      type="email"
                      required
                      placeholder="请输入邮箱地址"
                      value={applicationForm.email}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      手机号 *
                    </label>
                    <Input
                      name="phone"
                      type="tel"
                      required
                      placeholder="请输入手机号码"
                      value={applicationForm.phone}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      应聘职位 *
                    </label>
                    <select
                      name="position"
                      required
                      value={applicationForm.position}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">请选择职位</option>
                      {jobs.map((job) => (
                        <option key={job.id} value={job.title}>{job.title}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    工作经验
                  </label>
                  <select
                    name="experience"
                    value={applicationForm.experience}
                    onChange={handleInputChange}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">请选择工作经验</option>
                    <option value="应届毕业生">应届毕业生</option>
                    <option value="1-3年">1-3年</option>
                    <option value="3-5年">3-5年</option>
                    <option value="5-10年">5-10年</option>
                    <option value="10年以上">10年以上</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    上传简历
                  </label>
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    支持PDF、DOC、DOCX格式，文件大小不超过5MB
                  </p>
                </div>

                <Button type="submit" className="w-full" size="lg">
                  <Send className="h-4 w-4 mr-2" />
                  提交申请
                </Button>
              </form>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
