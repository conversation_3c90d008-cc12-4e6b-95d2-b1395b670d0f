import { Calendar, ExternalLink, Eye, Share2, Tag } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'

export default function MediaPage() {
  const mediaReports = [
    {
      id: '1',
      title: '本地生活服务平台完成B轮融资，估值达10亿元',
      summary: '本地生活服务平台宣布完成B轮融资，本轮融资将主要用于技术研发、市场拓展和服务质量提升。',
      source: '36氪',
      date: '2024-01-15',
      category: '融资',
      views: 15420,
      image: '/api/placeholder/400/250',
      url: '#'
    },
    {
      id: '2',
      title: '数字化转型助力本地服务业发展，平台模式成新趋势',
      summary: '随着数字化转型的深入，本地生活服务平台通过技术创新，为传统服务业注入新活力。',
      source: '人民日报',
      date: '2024-01-12',
      category: '行业分析',
      views: 23150,
      image: '/api/placeholder/400/250',
      url: '#'
    },
    {
      id: '3',
      title: '本地生活服务平台荣获"年度最佳创新企业"奖',
      summary: '在2023年度科技创新大会上，本地生活服务平台凭借其创新的商业模式和优质的服务获得殊荣。',
      source: '科技日报',
      date: '2024-01-10',
      category: '获奖',
      views: 8760,
      image: '/api/placeholder/400/250',
      url: '#'
    },
    {
      id: '4',
      title: '疫情后本地服务需求激增，平台订单量同比增长300%',
      summary: '疫情后消费者对本地生活服务的需求大幅增长，平台通过优化服务流程，满足用户多样化需求。',
      source: '新浪科技',
      date: '2024-01-08',
      category: '业务发展',
      views: 19230,
      image: '/api/placeholder/400/250',
      url: '#'
    },
    {
      id: '5',
      title: '本地生活服务平台推出"绿色服务"计划，倡导环保理念',
      summary: '平台启动绿色服务计划，鼓励服务商使用环保材料和工具，为用户提供更加环保的服务选择。',
      source: '环球网',
      date: '2024-01-05',
      category: '社会责任',
      views: 12450,
      image: '/api/placeholder/400/250',
      url: '#'
    },
    {
      id: '6',
      title: 'CEO专访：如何用科技改变传统服务业',
      summary: '本地生活服务平台CEO分享了公司的发展历程和未来规划，探讨科技如何赋能传统服务业。',
      source: '创业邦',
      date: '2024-01-03',
      category: '人物专访',
      views: 16780,
      image: '/api/placeholder/400/250',
      url: '#'
    }
  ]

  const mediaPartners = [
    { name: '人民日报', logo: '📰' },
    { name: '新华社', logo: '📺' },
    { name: '央视网', logo: '📻' },
    { name: '36氪', logo: '💼' },
    { name: '创业邦', logo: '🚀' },
    { name: '科技日报', logo: '🔬' },
    { name: '新浪科技', logo: '💻' },
    { name: '腾讯科技', logo: '🌐' }
  ]

  const categories = [
    { name: '全部', count: 156 },
    { name: '融资', count: 12 },
    { name: '行业分析', count: 34 },
    { name: '获奖', count: 8 },
    { name: '业务发展', count: 45 },
    { name: '社会责任', count: 23 },
    { name: '人物专访', count: 15 },
    { name: '产品发布', count: 19 }
  ]

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      '融资': 'bg-green-100 text-green-800',
      '行业分析': 'bg-blue-100 text-blue-800',
      '获奖': 'bg-yellow-100 text-yellow-800',
      '业务发展': 'bg-purple-100 text-purple-800',
      '社会责任': 'bg-pink-100 text-pink-800',
      '人物专访': 'bg-indigo-100 text-indigo-800',
      '产品发布': 'bg-orange-100 text-orange-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">媒体报道</h1>
          <p className="text-xl text-blue-100 max-w-2xl mx-auto">
            关注我们的发展历程，了解行业最新动态
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 侧边栏 */}
          <div className="lg:col-span-1">
            {/* 分类筛选 */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Tag className="h-5 w-5 mr-2" />
                  报道分类
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.name}
                      className="w-full flex items-center justify-between px-3 py-2 text-left rounded-md hover:bg-gray-100 transition-colors"
                    >
                      <span>{category.name}</span>
                      <span className="text-sm text-gray-500">({category.count})</span>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 媒体合作伙伴 */}
            <Card>
              <CardHeader>
                <CardTitle>媒体合作伙伴</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  {mediaPartners.map((partner, index) => (
                    <div key={index} className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl mb-1">{partner.logo}</div>
                      <div className="text-xs text-gray-600">{partner.name}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 主要内容 */}
          <div className="lg:col-span-3">
            {/* 头条报道 */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">头条报道</h2>
              <Card className="overflow-hidden">
                <div className="md:flex">
                  <div className="md:w-1/2">
                    <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                      <span className="text-6xl opacity-50">📰</span>
                    </div>
                  </div>
                  <div className="md:w-1/2 p-6">
                    <div className="flex items-center space-x-2 mb-3">
                      <Badge className={getCategoryColor(mediaReports[0].category)}>
                        {mediaReports[0].category}
                      </Badge>
                      <span className="text-sm text-gray-500">{mediaReports[0].source}</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      {mediaReports[0].title}
                    </h3>
                    <p className="text-gray-700 mb-4 line-clamp-3">
                      {mediaReports[0].summary}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {mediaReports[0].date}
                        </span>
                        <span className="flex items-center">
                          <Eye className="h-4 w-4 mr-1" />
                          {mediaReports[0].views.toLocaleString()}
                        </span>
                      </div>
                      <Button variant="outline" size="sm">
                        <ExternalLink className="h-4 w-4 mr-1" />
                        阅读全文
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            {/* 最新报道 */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">最新报道</h2>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">排序：</span>
                  <select className="border border-gray-300 rounded-md px-3 py-1 text-sm">
                    <option>最新发布</option>
                    <option>最多阅读</option>
                    <option>最多分享</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {mediaReports.slice(1).map((report) => (
                  <Card key={report.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                      <span className="text-4xl opacity-50">📰</span>
                    </div>
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-2 mb-3">
                        <Badge className={getCategoryColor(report.category)}>
                          {report.category}
                        </Badge>
                        <span className="text-sm text-gray-500">{report.source}</span>
                      </div>
                      <h3 className="font-bold text-lg text-gray-900 mb-2 line-clamp-2">
                        {report.title}
                      </h3>
                      <p className="text-gray-700 text-sm mb-4 line-clamp-3">
                        {report.summary}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 text-xs text-gray-500">
                          <span className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {report.date}
                          </span>
                          <span className="flex items-center">
                            <Eye className="h-3 w-3 mr-1" />
                            {report.views.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Share2 className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <ExternalLink className="h-4 w-4 mr-1" />
                            阅读
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* 分页 */}
              <div className="flex justify-center mt-8">
                <div className="flex space-x-2">
                  <Button variant="outline" disabled>上一页</Button>
                  <Button variant="outline" className="bg-primary text-white">1</Button>
                  <Button variant="outline">2</Button>
                  <Button variant="outline">3</Button>
                  <Button variant="outline">下一页</Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 媒体联系 */}
        <section className="mt-16">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">媒体联系</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <p className="text-gray-700 mb-6">
                  如果您是媒体记者，希望了解更多关于我们的信息，欢迎联系我们的媒体团队
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                  <div className="text-center">
                    <h4 className="font-semibold mb-2">媒体联系人</h4>
                    <p className="text-gray-600">张女士</p>
                    <p className="text-gray-600">媒体关系总监</p>
                  </div>
                  <div className="text-center">
                    <h4 className="font-semibold mb-2">联系方式</h4>
                    <p className="text-gray-600">邮箱：<EMAIL></p>
                    <p className="text-gray-600">电话：010-12345678</p>
                  </div>
                </div>
                <Button className="mt-6">
                  联系媒体团队
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
