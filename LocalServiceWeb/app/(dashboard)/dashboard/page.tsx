import { Calendar, Clock, Star, TrendingUp, Users, ShoppingCart, Heart, MessageCircle, User, Settings } from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Badge } from '../../../components/ui/badge'

export default function DashboardPage() {
  const stats = [
    {
      title: '总订单',
      value: '12',
      change: '+2',
      changeType: 'increase',
      icon: <ShoppingCart className="h-6 w-6" />
    },
    {
      title: '待评价',
      value: '3',
      change: '+1',
      changeType: 'increase',
      icon: <Star className="h-6 w-6" />
    },
    {
      title: '收藏服务',
      value: '8',
      change: '+0',
      changeType: 'neutral',
      icon: <Heart className="h-6 w-6" />
    },
    {
      title: '未读消息',
      value: '2',
      change: '+2',
      changeType: 'increase',
      icon: <MessageCircle className="h-6 w-6" />
    }
  ]

  const recentOrders = [
    {
      id: '1',
      service: '专业家电维修服务',
      provider: '张师傅',
      date: '2024-01-15',
      status: 'completed',
      amount: 120,
      rating: 5
    },
    {
      id: '2',
      service: '深度清洁服务',
      provider: '李阿姨',
      date: '2024-01-12',
      status: 'in_progress',
      amount: 200,
      rating: null
    },
    {
      id: '3',
      service: '上门理发服务',
      provider: '王师傅',
      date: '2024-01-10',
      status: 'pending',
      amount: 60,
      rating: null
    }
  ]

  const favoriteServices = [
    {
      id: '1',
      title: '专业家电维修服务',
      provider: '张师傅',
      rating: 4.8,
      price: 80,
      image: '/api/placeholder/100/100'
    },
    {
      id: '2',
      title: '深度清洁服务',
      provider: '李阿姨',
      rating: 4.9,
      price: 120,
      image: '/api/placeholder/100/100'
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>
      case 'in_progress':
        return <Badge className="bg-blue-100 text-blue-800">进行中</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">待确认</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">个人中心</h1>
          <p className="text-gray-600">管理您的订单、收藏和个人信息</p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <div className="flex items-center">
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      {stat.change !== '+0' && (
                        <span className={`ml-2 text-sm ${
                          stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {stat.change}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-primary">
                    {stat.icon}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 最近订单 */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>最近订单</CardTitle>
                <Button variant="outline" size="sm">
                  查看全部
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentOrders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 mb-1">{order.service}</h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>服务商：{order.provider}</span>
                          <span>日期：{order.date}</span>
                          <span>金额：¥{order.amount}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        {getStatusBadge(order.status)}
                        {order.rating && (
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <span className="text-sm text-gray-600 ml-1">{order.rating}</span>
                          </div>
                        )}
                        <Button variant="outline" size="sm">
                          {order.status === 'completed' ? '查看详情' : '管理订单'}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 快捷操作 */}
            <Card>
              <CardHeader>
                <CardTitle>快捷操作</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Link href="/services">
                    <Button className="w-full justify-start" variant="outline">
                      <Calendar className="h-4 w-4 mr-2" />
                      预订新服务
                    </Button>
                  </Link>
                  <Link href="/orders">
                    <Button className="w-full justify-start" variant="outline">
                      <Clock className="h-4 w-4 mr-2" />
                      查看订单历史
                    </Button>
                  </Link>
                  <Link href="/orders?tab=completed&filter=unrated">
                    <Button className="w-full justify-start" variant="outline">
                      <Star className="h-4 w-4 mr-2" />
                      待评价服务
                    </Button>
                  </Link>
                  <Link href="/messages">
                    <Button className="w-full justify-start" variant="outline">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      消息中心
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* 收藏的服务 */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>收藏的服务</CardTitle>
                <Button variant="outline" size="sm">
                  查看全部
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {favoriteServices.map((service) => (
                    <div key={service.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span className="text-lg">📷</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm text-gray-900 truncate">{service.title}</h4>
                        <div className="flex items-center justify-between text-xs text-gray-600">
                          <span>{service.provider}</span>
                          <div className="flex items-center">
                            <Star className="h-3 w-3 text-yellow-400 fill-current mr-1" />
                            {service.rating}
                          </div>
                        </div>
                        <div className="text-sm font-medium text-primary">¥{service.price}/小时</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 账户信息 */}
            <Card>
              <CardHeader>
                <CardTitle>账户信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">会员等级</span>
                    <Badge variant="outline">普通会员</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">积分余额</span>
                    <span className="font-medium">1,250</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">优惠券</span>
                    <span className="font-medium">3张</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">钱包余额</span>
                    <span className="font-medium">¥0.00</span>
                  </div>
                </div>
                <Button variant="outline" className="w-full mt-4">
                  充值
                </Button>
              </CardContent>
            </Card>

            {/* 账户管理 */}
            <Card>
              <CardHeader>
                <CardTitle>账户管理</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Link href="/profile">
                    <Button variant="outline" className="w-full justify-start">
                      <User className="h-4 w-4 mr-2" />
                      个人资料
                    </Button>
                  </Link>
                  <Link href="/settings">
                    <Button variant="outline" className="w-full justify-start">
                      <Settings className="h-4 w-4 mr-2" />
                      账户设置
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
