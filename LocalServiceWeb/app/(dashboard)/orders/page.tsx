'use client'

import { useState } from 'react'
import { Search, Filter, Calendar, Star, MessageCircle, ArrowLeft, Clock, CheckCircle, XCircle } from 'lucide-react'
import Link from 'next/link'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Badge } from '../../../components/ui/badge'

export default function OrdersPage() {
  const [activeTab, setActiveTab] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')

  const orders = [
    {
      id: 'ORD001',
      service: '专业家电维修服务',
      provider: '张师傅',
      providerAvatar: '👨‍🔧',
      date: '2024-01-15',
      time: '14:00-16:00',
      status: 'completed',
      amount: 120,
      rating: 5,
      address: '朝阳区建国路88号',
      description: '空调不制冷，需要检修',
      category: '家庭维修'
    },
    {
      id: 'ORD002',
      service: '深度清洁服务',
      provider: '李阿姨',
      providerAvatar: '👩‍💼',
      date: '2024-01-12',
      time: '09:00-12:00',
      status: 'in_progress',
      amount: 200,
      rating: null,
      address: '海淀区中关村大街1号',
      description: '新房开荒清洁',
      category: '清洁服务'
    },
    {
      id: 'ORD003',
      service: '上门理发服务',
      provider: '王师傅',
      providerAvatar: '✂️',
      date: '2024-01-10',
      time: '19:00-20:00',
      status: 'pending',
      amount: 60,
      rating: null,
      address: '西城区西单北大街',
      description: '男士理发',
      category: '美容美发'
    },
    {
      id: 'ORD004',
      service: '钢琴教学',
      provider: '陈老师',
      providerAvatar: '🎹',
      date: '2024-01-08',
      time: '15:00-16:00',
      status: 'cancelled',
      amount: 150,
      rating: null,
      address: '东城区王府井大街',
      description: '儿童钢琴入门课程',
      category: '家教培训'
    }
  ]

  const tabs = [
    { id: 'all', label: '全部订单', count: orders.length },
    { id: 'pending', label: '待确认', count: orders.filter(o => o.status === 'pending').length },
    { id: 'in_progress', label: '进行中', count: orders.filter(o => o.status === 'in_progress').length },
    { id: 'completed', label: '已完成', count: orders.filter(o => o.status === 'completed').length },
    { id: 'cancelled', label: '已取消', count: orders.filter(o => o.status === 'cancelled').length }
  ]

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { label: '已完成', className: 'bg-green-100 text-green-800', icon: CheckCircle },
      in_progress: { label: '进行中', className: 'bg-blue-100 text-blue-800', icon: Clock },
      pending: { label: '待确认', className: 'bg-yellow-100 text-yellow-800', icon: Clock },
      cancelled: { label: '已取消', className: 'bg-red-100 text-red-800', icon: XCircle }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig]
    const Icon = config.icon
    
    return (
      <Badge className={config.className}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    )
  }

  const filteredOrders = orders.filter(order => {
    const matchesTab = activeTab === 'all' || order.status === activeTab
    const matchesSearch = order.service.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.provider.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesTab && matchesSearch
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
            </Link>
            <h1 className="text-3xl font-bold text-gray-900">我的订单</h1>
          </div>
          <p className="text-gray-600">查看和管理您的所有服务订单</p>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="search"
                  placeholder="搜索订单..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                筛选
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                日期范围
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 订单状态标签 */}
        <div className="flex flex-wrap gap-2 mb-6">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? "default" : "outline"}
              onClick={() => setActiveTab(tab.id)}
              className="flex items-center gap-2"
            >
              {tab.label}
              <Badge variant="secondary" className="ml-1">
                {tab.count}
              </Badge>
            </Button>
          ))}
        </div>

        {/* 订单列表 */}
        <div className="space-y-4">
          {filteredOrders.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <div className="text-gray-400 mb-4">
                  <Calendar className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无订单</h3>
                <p className="text-gray-600 mb-4">您还没有任何订单记录</p>
                <Button asChild>
                  <Link href="/services">浏览服务</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredOrders.map((order) => (
              <Card key={order.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                    {/* 订单基本信息 */}
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-1">
                            {order.service}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <span>订单号：{order.id}</span>
                            <Badge variant="outline">{order.category}</Badge>
                          </div>
                        </div>
                        {getStatusBadge(order.status)}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <span className="text-gray-600 w-16">服务商：</span>
                            <div className="flex items-center">
                              <span className="mr-2">{order.providerAvatar}</span>
                              <span className="font-medium">{order.provider}</span>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <span className="text-gray-600 w-16">时间：</span>
                            <span>{order.date} {order.time}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-gray-600 w-16">地址：</span>
                            <span>{order.address}</span>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <span className="text-gray-600 w-16">金额：</span>
                            <span className="font-semibold text-primary">¥{order.amount}</span>
                          </div>
                          {order.rating && (
                            <div className="flex items-center">
                              <span className="text-gray-600 w-16">评分：</span>
                              <div className="flex items-center">
                                {[...Array(order.rating)].map((_, i) => (
                                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                                ))}
                                <span className="ml-1 text-sm">{order.rating}.0</span>
                              </div>
                            </div>
                          )}
                          <div className="flex items-start">
                            <span className="text-gray-600 w-16">描述：</span>
                            <span className="text-gray-800">{order.description}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex flex-col space-y-2 lg:w-48">
                      {order.status === 'pending' && (
                        <>
                          <Button size="sm" className="w-full">确认订单</Button>
                          <Button variant="outline" size="sm" className="w-full">取消订单</Button>
                        </>
                      )}
                      {order.status === 'in_progress' && (
                        <>
                          <Button size="sm" className="w-full">
                            <MessageCircle className="h-4 w-4 mr-2" />
                            联系服务商
                          </Button>
                          <Button variant="outline" size="sm" className="w-full">查看详情</Button>
                        </>
                      )}
                      {order.status === 'completed' && (
                        <>
                          {!order.rating && (
                            <Button size="sm" className="w-full">
                              <Star className="h-4 w-4 mr-2" />
                              评价服务
                            </Button>
                          )}
                          <Button variant="outline" size="sm" className="w-full">再次预订</Button>
                          <Button variant="outline" size="sm" className="w-full">查看详情</Button>
                        </>
                      )}
                      {order.status === 'cancelled' && (
                        <Button variant="outline" size="sm" className="w-full">查看详情</Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* 分页 */}
        {filteredOrders.length > 0 && (
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              <Button variant="outline" disabled>上一页</Button>
              <Button variant="outline" className="bg-primary text-white">1</Button>
              <Button variant="outline">2</Button>
              <Button variant="outline">3</Button>
              <Button variant="outline">下一页</Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
