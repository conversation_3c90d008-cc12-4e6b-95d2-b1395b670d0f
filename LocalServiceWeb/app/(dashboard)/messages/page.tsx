'use client'

import { useState } from 'react'
import { Search, MoreVertical, ArrowLeft, Send, Paperclip, Phone, Video } from 'lucide-react'
import Link from 'next/link'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Badge } from '../../../components/ui/badge'

export default function MessagesPage() {
  const [selectedChat, setSelectedChat] = useState('1')
  const [newMessage, setNewMessage] = useState('')

  const conversations = [
    {
      id: '1',
      name: '张师傅',
      avatar: '👨‍🔧',
      lastMessage: '好的，我明天下午2点准时到达',
      timestamp: '2分钟前',
      unread: 0,
      online: true,
      service: '家电维修服务'
    },
    {
      id: '2',
      name: '李阿姨',
      avatar: '👩‍💼',
      lastMessage: '清洁工作已经完成，请您验收',
      timestamp: '1小时前',
      unread: 2,
      online: false,
      service: '深度清洁服务'
    },
    {
      id: '3',
      name: '王师傅',
      avatar: '✂️',
      lastMessage: '您好，关于理发时间我们可以商量一下',
      timestamp: '3小时前',
      unread: 1,
      online: true,
      service: '上门理发服务'
    },
    {
      id: '4',
      name: '客服小助手',
      avatar: '🤖',
      lastMessage: '您的订单已经处理完成，如有问题请随时联系我们',
      timestamp: '昨天',
      unread: 0,
      online: true,
      service: '客服支持'
    }
  ]

  const messages = [
    {
      id: '1',
      senderId: 'user',
      content: '您好，请问明天下午方便上门维修吗？',
      timestamp: '14:30',
      type: 'text'
    },
    {
      id: '2',
      senderId: '1',
      content: '您好！明天下午我有时间，大概几点比较方便？',
      timestamp: '14:32',
      type: 'text'
    },
    {
      id: '3',
      senderId: 'user',
      content: '2点到4点之间都可以',
      timestamp: '14:33',
      type: 'text'
    },
    {
      id: '4',
      senderId: '1',
      content: '好的，我明天下午2点准时到达',
      timestamp: '14:35',
      type: 'text'
    }
  ]

  const currentChat = conversations.find(c => c.id === selectedChat)

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (newMessage.trim()) {
      // 处理发送消息逻辑
      console.log('Sending message:', newMessage)
      setNewMessage('')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
              </Button>
            </Link>
            <h1 className="text-3xl font-bold text-gray-900">消息中心</h1>
          </div>
          <p className="text-gray-600">与服务商和客服沟通交流</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
          {/* 对话列表 */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>对话列表</span>
                <Badge variant="secondary">
                  {conversations.filter(c => c.unread > 0).length} 未读
                </Badge>
              </CardTitle>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="search"
                  placeholder="搜索对话..."
                  className="pl-10"
                />
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-1">
                {conversations.map((conversation) => (
                  <div
                    key={conversation.id}
                    onClick={() => setSelectedChat(conversation.id)}
                    className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors border-l-4 ${
                      selectedChat === conversation.id 
                        ? 'bg-blue-50 border-l-primary' 
                        : 'border-l-transparent'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="relative">
                        <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-lg">
                          {conversation.avatar}
                        </div>
                        {conversation.online && (
                          <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-medium text-gray-900 truncate">
                            {conversation.name}
                          </h4>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500">
                              {conversation.timestamp}
                            </span>
                            {conversation.unread > 0 && (
                              <Badge className="bg-red-500 text-white text-xs px-1.5 py-0.5 min-w-[20px] h-5 flex items-center justify-center">
                                {conversation.unread}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 truncate mb-1">
                          {conversation.lastMessage}
                        </p>
                        <p className="text-xs text-gray-500">
                          {conversation.service}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 聊天界面 */}
          <Card className="lg:col-span-2 flex flex-col">
            {currentChat ? (
              <>
                {/* 聊天头部 */}
                <CardHeader className="border-b">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-lg">
                          {currentChat.avatar}
                        </div>
                        {currentChat.online && (
                          <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                        )}
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{currentChat.name}</h3>
                        <p className="text-sm text-gray-600">{currentChat.service}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Video className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                {/* 消息列表 */}
                <CardContent className="flex-1 p-4 overflow-y-auto">
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${
                          message.senderId === 'user' ? 'justify-end' : 'justify-start'
                        }`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            message.senderId === 'user'
                              ? 'bg-primary text-white'
                              : 'bg-gray-200 text-gray-900'
                          }`}
                        >
                          <p className="text-sm">{message.content}</p>
                          <p className={`text-xs mt-1 ${
                            message.senderId === 'user' ? 'text-blue-100' : 'text-gray-500'
                          }`}>
                            {message.timestamp}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>

                {/* 消息输入 */}
                <div className="border-t p-4">
                  <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
                    <Button type="button" variant="outline" size="sm">
                      <Paperclip className="h-4 w-4" />
                    </Button>
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="输入消息..."
                      className="flex-1"
                    />
                    <Button type="submit" disabled={!newMessage.trim()}>
                      <Send className="h-4 w-4" />
                    </Button>
                  </form>
                </div>
              </>
            ) : (
              <CardContent className="flex-1 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <div className="text-4xl mb-4">💬</div>
                  <p>选择一个对话开始聊天</p>
                </div>
              </CardContent>
            )}
          </Card>
        </div>
      </div>
    </div>
  )
}
