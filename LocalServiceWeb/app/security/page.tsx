import { Shield, Lock, Eye, Users, CheckCircle, AlertTriangle, Phone, Mail } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'

export default function SecurityPage() {
  const securityMeasures = [
    {
      icon: <Shield className="h-8 w-8" />,
      title: '实名认证',
      description: '所有服务商必须通过实名认证，确保身份真实可靠',
      features: ['身份证验证', '人脸识别', '资质审核', '背景调查']
    },
    {
      icon: <Lock className="h-8 w-8" />,
      title: '数据加密',
      description: '采用银行级加密技术保护用户数据安全',
      features: ['SSL加密传输', '数据库加密', '密码加密存储', '安全备份']
    },
    {
      icon: <Eye className="h-8 w-8" />,
      title: '隐私保护',
      description: '严格保护用户隐私，不泄露个人信息',
      features: ['隐私政策', '数据最小化', '用户授权', '定期审计']
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: '服务保障',
      description: '完善的服务保障体系，保护用户权益',
      features: ['服务保险', '质量监控', '投诉处理', '退款保障']
    }
  ]

  const certifications = [
    {
      name: 'ISO 27001',
      description: '信息安全管理体系认证',
      status: 'certified',
      date: '2023-12-01'
    },
    {
      name: '等保三级',
      description: '网络安全等级保护认证',
      status: 'certified',
      date: '2023-11-15'
    },
    {
      name: 'PCI DSS',
      description: '支付卡行业数据安全标准',
      status: 'certified',
      date: '2023-10-20'
    },
    {
      name: 'SOC 2',
      description: '服务组织控制报告',
      status: 'in_progress',
      date: '2024-03-01'
    }
  ]

  const safetyTips = [
    {
      title: '选择服务商',
      tips: [
        '查看服务商认证状态和评价',
        '选择评分较高的服务商',
        '仔细阅读服务描述和条款',
        '通过平台内消息沟通'
      ]
    },
    {
      title: '服务过程',
      tips: [
        '确认服务商身份后再开始服务',
        '贵重物品请妥善保管',
        '如有异常情况及时联系客服',
        '保留服务过程的相关证据'
      ]
    },
    {
      title: '支付安全',
      tips: [
        '使用平台推荐的支付方式',
        '不要私下转账给服务商',
        '保留支付凭证和订单记录',
        '发现异常交易及时举报'
      ]
    },
    {
      title: '个人信息',
      tips: [
        '不要泄露过多个人信息',
        '定期检查账户安全设置',
        '使用强密码并定期更换',
        '不要在公共网络下操作'
      ]
    }
  ]

  const emergencyContacts = [
    {
      type: '客服热线',
      contact: '************',
      available: '7×24小时',
      icon: <Phone className="h-5 w-5" />
    },
    {
      type: '紧急邮箱',
      contact: '<EMAIL>',
      available: '24小时内回复',
      icon: <Mail className="h-5 w-5" />
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">安全保障</h1>
          <p className="text-xl text-blue-100 max-w-2xl mx-auto">
            我们致力于为用户提供安全可靠的服务环境，保护您的权益和隐私
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        {/* 安全措施 */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">安全措施</h2>
            <p className="text-lg text-gray-600">多重安全保障，全方位保护用户安全</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {securityMeasures.map((measure, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="text-primary mb-4 flex justify-center">
                    {measure.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-center mb-3">{measure.title}</h3>
                  <p className="text-gray-600 text-center mb-4">{measure.description}</p>
                  <div className="space-y-2">
                    {measure.features.map((feature, fIndex) => (
                      <div key={fIndex} className="flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 认证资质 */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">认证资质</h2>
            <p className="text-lg text-gray-600">获得多项权威安全认证，确保服务质量</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {certifications.map((cert, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">{cert.name}</h3>
                  <p className="text-gray-600 text-sm mb-3">{cert.description}</p>
                  <div className="flex items-center justify-center space-x-2">
                    {cert.status === 'certified' ? (
                      <Badge className="bg-green-100 text-green-800">已认证</Badge>
                    ) : (
                      <Badge className="bg-yellow-100 text-yellow-800">进行中</Badge>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-2">{cert.date}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 安全提示 */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">安全提示</h2>
            <p className="text-lg text-gray-600">掌握安全知识，保护自身权益</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {safetyTips.map((category, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-orange-500 mr-2" />
                    {category.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {category.tips.map((tip, tIndex) => (
                      <div key={tIndex} className="flex items-start">
                        <div className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-700 text-sm">{tip}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 举报投诉 */}
        <section className="mb-16">
          <Card className="bg-red-50 border-red-200">
            <CardHeader>
              <CardTitle className="text-red-800 flex items-center">
                <AlertTriangle className="h-6 w-6 mr-2" />
                举报投诉
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h4 className="font-semibold mb-3 text-red-800">如遇以下情况，请立即举报：</h4>
                  <div className="space-y-2">
                    {[
                      '服务商要求私下交易',
                      '服务商行为异常或可疑',
                      '遭遇欺诈或诈骗行为',
                      '个人信息被泄露或滥用',
                      '服务质量严重不符合描述',
                      '其他违法违规行为'
                    ].map((item, index) => (
                      <div key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-red-700 text-sm">{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-3 text-red-800">举报方式：</h4>
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full justify-start border-red-300 text-red-700 hover:bg-red-100">
                      <Phone className="h-4 w-4 mr-2" />
                      拨打举报热线：************
                    </Button>
                    <Button variant="outline" className="w-full justify-start border-red-300 text-red-700 hover:bg-red-100">
                      <Mail className="h-4 w-4 mr-2" />
                      发送邮件：<EMAIL>
                    </Button>
                  </div>
                  <p className="text-xs text-red-600 mt-3">
                    我们承诺在24小时内响应您的举报，并严格保护举报人隐私。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* 紧急联系 */}
        <section>
          <Card>
            <CardHeader>
              <CardTitle className="text-center">紧急联系方式</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {emergencyContacts.map((contact, index) => (
                  <div key={index} className="text-center p-6 bg-gray-50 rounded-lg">
                    <div className="text-primary mb-3 flex justify-center">
                      {contact.icon}
                    </div>
                    <h3 className="font-semibold text-lg mb-2">{contact.type}</h3>
                    <p className="text-xl font-bold text-primary mb-1">{contact.contact}</p>
                    <p className="text-sm text-gray-600">{contact.available}</p>
                  </div>
                ))}
              </div>
              <div className="text-center mt-6">
                <p className="text-gray-600 mb-4">
                  如遇紧急情况，请优先拨打客服热线或报警电话
                </p>
                <div className="flex justify-center space-x-4">
                  <Button className="bg-red-600 hover:bg-red-700">
                    报警电话：110
                  </Button>
                  <Button variant="outline">
                    联系客服
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
