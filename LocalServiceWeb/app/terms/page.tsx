import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'

export default function TermsPage() {
  const sections = [
    {
      title: '1. 服务条款的接受',
      content: [
        '欢迎使用本地生活服务平台（以下简称"本平台"）。在使用本平台提供的各项服务前，请您仔细阅读并充分理解本服务条款。',
        '您通过网络页面点击确认或以其他方式选择接受本协议，即表示您与本平台已达成协议并同意接受本服务条款的全部约定内容。',
        '本平台有权根据需要不时地制订、修改本协议或各类规则，并以网站公示的方式进行公告，不再单独通知您。'
      ]
    },
    {
      title: '2. 服务内容',
      content: [
        '本平台为用户提供本地生活服务信息展示、预订、支付等相关服务。',
        '本平台仅作为信息展示和交易撮合平台，不直接提供具体的生活服务。',
        '具体服务由平台上的第三方服务商提供，服务质量由服务商负责。'
      ]
    },
    {
      title: '3. 用户注册与账户',
      content: [
        '用户需要注册账户才能使用本平台的完整服务。',
        '用户应提供真实、准确、完整的个人信息，并及时更新。',
        '用户对账户和密码的安全性负责，因账户或密码泄露造成的损失由用户承担。',
        '禁止用户将账户转让、出售或以其他方式提供给第三方使用。'
      ]
    },
    {
      title: '4. 用户行为规范',
      content: [
        '用户在使用本平台服务时，应遵守相关法律法规和本协议约定。',
        '禁止发布虚假信息、恶意评价或进行其他损害平台声誉的行为。',
        '禁止利用本平台从事任何违法违规活动。',
        '用户应尊重服务商的劳动成果，按约定支付服务费用。'
      ]
    },
    {
      title: '5. 服务商责任',
      content: [
        '服务商应具备提供相应服务的资质和能力。',
        '服务商应按照约定的时间、地点和标准提供服务。',
        '服务商应保护用户隐私，不得泄露用户个人信息。',
        '服务商对其提供的服务质量承担责任。'
      ]
    },
    {
      title: '6. 平台责任与免责',
      content: [
        '本平台努力确保服务的稳定性和安全性，但不保证服务不会中断。',
        '本平台对服务商提供的服务质量不承担直接责任。',
        '因不可抗力、网络故障等原因造成的服务中断，本平台不承担责任。',
        '本平台有权对违规用户进行警告、限制使用或封禁账户等处理。'
      ]
    },
    {
      title: '7. 费用与支付',
      content: [
        '用户使用本平台基础服务免费，但需要为具体的生活服务付费。',
        '服务费用由用户与服务商协商确定，通过本平台支付系统完成支付。',
        '本平台可能收取一定的平台服务费，具体标准以页面显示为准。',
        '用户应按时支付相关费用，逾期可能影响后续服务使用。'
      ]
    },
    {
      title: '8. 退款与争议处理',
      content: [
        '用户可根据平台退款政策申请退款，具体条件以相关规则为准。',
        '发生争议时，用户可通过平台客服进行投诉和协调。',
        '本平台将公正处理用户与服务商之间的争议。',
        '无法协调解决的争议，可通过法律途径解决。'
      ]
    },
    {
      title: '9. 知识产权',
      content: [
        '本平台的所有内容，包括但不限于文字、图片、音频、视频、软件等，均受知识产权法保护。',
        '用户不得擅自复制、传播、修改本平台的内容。',
        '用户上传的内容应确保不侵犯他人知识产权。',
        '如发现侵权行为，本平台有权立即删除相关内容并追究责任。'
      ]
    },
    {
      title: '10. 隐私保护',
      content: [
        '本平台重视用户隐私保护，制定了专门的隐私政策。',
        '用户个人信息仅用于提供服务和改善用户体验。',
        '未经用户同意，本平台不会向第三方泄露用户个人信息。',
        '用户有权查询、修改或删除自己的个人信息。'
      ]
    },
    {
      title: '11. 协议修改与终止',
      content: [
        '本平台有权根据业务发展需要修改本协议。',
        '协议修改后，将在平台上公布，用户继续使用即视为同意修改后的协议。',
        '用户可随时停止使用本平台服务并注销账户。',
        '本平台也可根据情况终止向用户提供服务。'
      ]
    },
    {
      title: '12. 法律适用与争议解决',
      content: [
        '本协议的签订、履行、解释及争议解决均适用中华人民共和国法律。',
        '因本协议产生的争议，双方应友好协商解决。',
        '协商不成的，任何一方均可向本平台所在地人民法院提起诉讼。',
        '本协议的任何条款无效，不影响其他条款的效力。'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">服务条款</h1>
          <p className="text-xl text-blue-100 max-w-2xl mx-auto">
            请仔细阅读以下服务条款，使用我们的服务即表示您同意这些条款
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* 更新日期 */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-gray-600 mb-2">最后更新日期：2024年1月1日</p>
                <p className="text-gray-600">生效日期：2024年1月1日</p>
              </div>
            </CardContent>
          </Card>

          {/* 前言 */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>前言</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed">
                本服务条款是您与本地生活服务平台之间关于使用本平台服务的法律协议。
                本协议阐述了您在使用我们服务时的权利和义务，以及我们向您提供服务的条件和限制。
                请您在使用我们的服务前仔细阅读本协议的全部内容。
              </p>
            </CardContent>
          </Card>

          {/* 条款内容 */}
          <div className="space-y-6">
            {sections.map((section, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-xl">{section.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {section.content.map((paragraph, pIndex) => (
                      <p key={pIndex} className="text-gray-700 leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* 联系信息 */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>联系我们</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed mb-4">
                如果您对本服务条款有任何疑问或建议，请通过以下方式联系我们：
              </p>
              <div className="space-y-2 text-gray-700">
                <p><strong>客服热线：</strong>400-123-4567</p>
                <p><strong>邮箱地址：</strong><EMAIL></p>
                <p><strong>公司地址：</strong>北京市朝阳区xxx大厦</p>
                <p><strong>工作时间：</strong>周一至周日 8:00-20:00</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
