import { Search, Filter, MapPin, Star, Grid, List, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Card, CardContent } from '../../../components/ui/card'
import { Badge } from '../../../components/ui/badge'

interface CategoryPageProps {
  params: {
    id: string
  }
}

export default function CategoryPage({ params }: CategoryPageProps) {
  // 分类数据映射
  const categoryData: { [key: string]: any } = {
    'home-repair': {
      name: '家庭维修',
      icon: '🔧',
      description: '专业的家庭维修服务，包括家电维修、水电维修、门窗维修等',
      color: 'bg-red-100 text-red-600',
      subcategories: ['空调维修', '洗衣机维修', '水管维修', '电路维修', '门锁维修', '家具维修']
    },
    'cleaning': {
      name: '清洁服务',
      icon: '🧹',
      description: '专业的清洁服务，让您的家居环境更加整洁舒适',
      color: 'bg-blue-100 text-blue-600',
      subcategories: ['家庭保洁', '开荒清洁', '地毯清洗', '玻璃清洁', '消毒杀菌', '深度清洁']
    },
    'beauty': {
      name: '美容美发',
      icon: '💄',
      description: '专业的美容美发服务，让您焕发自信光彩',
      color: 'bg-pink-100 text-pink-600',
      subcategories: ['上门理发', '美甲服务', '化妆造型', '皮肤护理', '美睫服务', '纹绣服务']
    },
    'education': {
      name: '家教培训',
      icon: '📚',
      description: '优质的教育培训服务，助力学习成长',
      color: 'bg-green-100 text-green-600',
      subcategories: ['小学数学', '英语口语', '钢琴教学', '书法培训', '编程教学', '美术培训']
    },
    'pet': {
      name: '宠物服务',
      icon: '🐕',
      description: '专业的宠物护理服务，给您的爱宠最好的照顾',
      color: 'bg-yellow-100 text-yellow-600',
      subcategories: ['宠物寄养', '宠物美容', '遛狗服务', '宠物训练', '宠物医疗', '宠物摄影']
    },
    'moving': {
      name: '搬家服务',
      icon: '📦',
      description: '专业的搬家服务，让您的搬迁更加轻松便捷',
      color: 'bg-purple-100 text-purple-600',
      subcategories: ['居民搬家', '钢琴搬运', '办公室搬迁', '长途搬家', '物品包装', '家具拆装']
    },
    'photography': {
      name: '摄影摄像',
      icon: '📷',
      description: '专业的摄影摄像服务，记录美好时刻',
      color: 'bg-indigo-100 text-indigo-600',
      subcategories: ['婚礼摄影', '活动拍摄', '产品摄影', '证件照', '视频制作', '航拍服务']
    },
    'fitness': {
      name: '健身运动',
      icon: '💪',
      description: '专业的健身指导服务，助您保持健康体魄',
      color: 'bg-orange-100 text-orange-600',
      subcategories: ['私人教练', '瑜伽教学', '健身指导', '舞蹈教学', '游泳教练', '营养咨询']
    }
  }

  const category = categoryData[params.id] || {
    name: '未知分类',
    icon: '❓',
    description: '该分类暂时不可用',
    color: 'bg-gray-100 text-gray-600',
    subcategories: []
  }

  // 模拟服务数据
  const services = [
    {
      id: '1',
      title: `专业${category.name}服务`,
      description: `提供优质的${category.name}服务，经验丰富，技术过硬，价格合理。`,
      price: 80,
      priceUnit: '小时',
      rating: 4.8,
      reviewCount: 156,
      location: '朝阳区',
      distance: '2.3km',
      tags: ['上门服务', '质量保证', '价格透明'],
      provider: {
        name: '张师傅',
        rating: 4.9,
        isVerified: true,
      },
      isOnline: true
    },
    {
      id: '2',
      title: `高端${category.name}服务`,
      description: `专业团队提供高端${category.name}服务，使用优质材料，细致入微。`,
      price: 120,
      priceUnit: '次',
      rating: 4.9,
      reviewCount: 203,
      location: '海淀区',
      distance: '5.1km',
      tags: ['专业团队', '优质材料', '细致服务'],
      provider: {
        name: '李师傅',
        rating: 4.8,
        isVerified: true,
      },
      isOnline: false
    },
    {
      id: '3',
      title: `便民${category.name}服务`,
      description: `便民${category.name}服务，方便快捷，工具齐全，服务周到。`,
      price: 60,
      priceUnit: '次',
      rating: 4.7,
      reviewCount: 89,
      location: '西城区',
      distance: '3.7km',
      tags: ['方便快捷', '工具齐全', '服务周到'],
      provider: {
        name: '王师傅',
        rating: 4.6,
        isVerified: false,
      },
      isOnline: true
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 面包屑导航 */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-gray-500 hover:text-primary">首页</Link>
            <span className="text-gray-300">/</span>
            <Link href="/categories" className="text-gray-500 hover:text-primary">服务分类</Link>
            <span className="text-gray-300">/</span>
            <span className="text-gray-900">{category.name}</span>
          </div>
        </div>
      </div>

      {/* 分类标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="flex items-center mb-4">
            <Link href="/categories">
              <Button variant="ghost" className="text-white hover:bg-white/20 mr-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回分类
              </Button>
            </Link>
          </div>
          <div className="flex items-center space-x-4 mb-4">
            <div className={`w-16 h-16 rounded-full ${category.color} flex items-center justify-center text-3xl`}>
              {category.icon}
            </div>
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-2">{category.name}</h1>
              <p className="text-xl text-blue-100">{category.description}</p>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">
        {/* 子分类 */}
        <section className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">热门子分类</h2>
          <div className="flex flex-wrap gap-3">
            {category.subcategories.map((subcategory: string, index: number) => (
              <Button key={index} variant="outline" className="rounded-full">
                {subcategory}
              </Button>
            ))}
          </div>
        </section>

        {/* 搜索和筛选 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="search"
                placeholder={`搜索${category.name}服务...`}
                className="pl-10"
              />
            </div>
            
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="选择位置..."
                className="pl-10 w-full lg:w-48"
              />
            </div>
            
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              筛选
            </Button>
            
            <div className="flex border rounded-lg">
              <Button variant="ghost" size="sm" className="rounded-r-none">
                <Grid className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="rounded-l-none">
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* 侧边栏筛选 */}
          <div className="lg:w-64">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="font-semibold text-lg mb-4">筛选条件</h3>
              
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-3">价格范围</h4>
                  <div className="space-y-2">
                    {['¥0 - ¥50', '¥50 - ¥100', '¥100 - ¥200', '¥200+'].map((range) => (
                      <label key={range} className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">{range}</span>
                      </label>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">评分</h4>
                  <div className="space-y-2">
                    {[5, 4, 3].map((rating) => (
                      <label key={rating} className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <div className="flex items-center">
                          {[...Array(rating)].map((_, i) => (
                            <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                          ))}
                          <span className="text-sm ml-1">及以上</span>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">服务特色</h4>
                  <div className="space-y-2">
                    {['上门服务', '24小时服务', '认证服务商', '质量保证'].map((feature) => (
                      <label key={feature} className="flex items-center">
                        <input type="checkbox" className="mr-2" />
                        <span className="text-sm">{feature}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 服务列表 */}
          <div className="flex-1">
            <div className="flex justify-between items-center mb-6">
              <p className="text-gray-600">找到 {services.length} 个{category.name}服务</p>
              <select className="border rounded-md px-3 py-2">
                <option>默认排序</option>
                <option>价格从低到高</option>
                <option>价格从高到低</option>
                <option>评分最高</option>
                <option>距离最近</option>
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {services.map((service) => (
                <Card key={service.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="aspect-video bg-gray-200 rounded-t-lg relative">
                    <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center rounded-t-lg">
                      <span className="text-4xl opacity-50">{category.icon}</span>
                    </div>
                    <Badge className="absolute top-3 left-3">
                      {category.name}
                    </Badge>
                  </div>
                  
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-lg line-clamp-1">
                        {service.title}
                      </h3>
                      <div className="text-right">
                        <div className="text-lg font-bold text-primary">
                          ¥{service.price}
                        </div>
                        <div className="text-sm text-gray-500">
                          /{service.priceUnit}
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {service.description}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 mr-1" />
                        {service.rating} ({service.reviewCount}评价)
                      </div>
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        {service.location}
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mb-3">
                      {service.tags.slice(0, 2).map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs mr-2">
                          {service.provider.name.charAt(0)}
                        </div>
                        <span className="text-sm font-medium">{service.provider.name}</span>
                        {service.isOnline && (
                          <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                        )}
                      </div>
                      <Link href={`/services/${service.id}`}>
                        <Button size="sm">查看详情</Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            {/* 分页 */}
            <div className="flex justify-center mt-8">
              <div className="flex space-x-2">
                <Button variant="outline" disabled>上一页</Button>
                <Button variant="outline" className="bg-primary text-white">1</Button>
                <Button variant="outline">2</Button>
                <Button variant="outline">3</Button>
                <Button variant="outline">下一页</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
