import Link from 'next/link'
import { Search, TrendingUp } from 'lucide-react'
import { Input } from '../../components/ui/input'
import { Card, CardContent } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'

export default function CategoriesPage() {
  const categories = [
    {
      id: 'home-repair',
      name: '家庭维修',
      icon: '🔧',
      color: 'bg-red-100 text-red-600',
      count: 156,
      description: '家电维修、水电维修、门窗维修',
      subcategories: ['空调维修', '洗衣机维修', '水管维修', '电路维修', '门锁维修'],
      trending: true
    },
    {
      id: 'cleaning',
      name: '清洁服务',
      icon: '🧹',
      color: 'bg-blue-100 text-blue-600',
      count: 89,
      description: '家庭清洁、办公室清洁、深度清洁',
      subcategories: ['家庭保洁', '开荒清洁', '地毯清洗', '玻璃清洁', '消毒杀菌'],
      trending: false
    },
    {
      id: 'beauty',
      name: '美容美发',
      icon: '💄',
      color: 'bg-pink-100 text-pink-600',
      count: 234,
      description: '理发、美容、美甲、化妆',
      subcategories: ['上门理发', '美甲服务', '化妆造型', '皮肤护理', '美睫服务'],
      trending: true
    },
    {
      id: 'education',
      name: '家教培训',
      icon: '📚',
      color: 'bg-green-100 text-green-600',
      count: 67,
      description: '学科辅导、技能培训、语言学习',
      subcategories: ['小学数学', '英语口语', '钢琴教学', '书法培训', '编程教学'],
      trending: false
    },
    {
      id: 'pet',
      name: '宠物服务',
      icon: '🐕',
      color: 'bg-yellow-100 text-yellow-600',
      count: 45,
      description: '宠物寄养、美容、训练、医疗',
      subcategories: ['宠物寄养', '宠物美容', '遛狗服务', '宠物训练', '宠物医疗'],
      trending: true
    },
    {
      id: 'moving',
      name: '搬家服务',
      icon: '📦',
      color: 'bg-purple-100 text-purple-600',
      count: 78,
      description: '居民搬家、公司搬迁、物品包装',
      subcategories: ['居民搬家', '钢琴搬运', '办公室搬迁', '长途搬家', '物品包装'],
      trending: false
    },
    {
      id: 'photography',
      name: '摄影摄像',
      icon: '📷',
      color: 'bg-indigo-100 text-indigo-600',
      count: 123,
      description: '婚礼摄影、活动拍摄、证件照',
      subcategories: ['婚礼摄影', '活动拍摄', '产品摄影', '证件照', '视频制作'],
      trending: true
    },
    {
      id: 'fitness',
      name: '健身运动',
      icon: '💪',
      color: 'bg-orange-100 text-orange-600',
      count: 92,
      description: '私人教练、瑜伽、健身指导',
      subcategories: ['私人教练', '瑜伽教学', '健身指导', '舞蹈教学', '游泳教练'],
      trending: false
    },
    {
      id: 'car-service',
      name: '汽车服务',
      icon: '🚗',
      color: 'bg-gray-100 text-gray-600',
      count: 134,
      description: '汽车维修、保养、清洗',
      subcategories: ['汽车维修', '汽车保养', '汽车清洗', '轮胎更换', '汽车美容'],
      trending: false
    },
    {
      id: 'other',
      name: '其他服务',
      icon: '⭐',
      color: 'bg-teal-100 text-teal-600',
      count: 234,
      description: '更多生活服务等您发现',
      subcategories: ['跑腿代办', '设备租赁', '活动策划', '翻译服务', '法律咨询'],
      trending: false
    }
  ]

  const trendingCategories = categories.filter(cat => cat.trending)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            服务分类
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
            涵盖生活各个方面的专业服务，总有一款适合您
          </p>
          
          {/* 搜索框 */}
          <div className="max-w-md mx-auto relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="search"
              placeholder="搜索服务分类..."
              className="pl-10"
            />
          </div>
        </div>

        {/* 热门分类 */}
        <div className="mb-12">
          <div className="flex items-center mb-6">
            <TrendingUp className="h-6 w-6 text-red-500 mr-2" />
            <h2 className="text-2xl font-bold text-gray-900">热门分类</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {trendingCategories.map((category) => (
              <Link key={category.id} href={`/categories/${category.id}`}>
                <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer h-full relative overflow-hidden">
                  <div className="absolute top-3 right-3">
                    <Badge variant="destructive" className="text-xs">
                      热门
                    </Badge>
                  </div>
                  <CardContent className="p-6">
                    <div className="text-center">
                      {/* Icon */}
                      <div className={`w-16 h-16 mx-auto mb-4 rounded-full ${category.color} flex items-center justify-center text-2xl`}>
                        {category.icon}
                      </div>
                      
                      {/* Category Info */}
                      <h3 className="font-semibold text-lg text-gray-900 mb-2 group-hover:text-primary transition-colors">
                        {category.name}
                      </h3>
                      
                      <p className="text-sm text-gray-600 mb-3">
                        {category.description}
                      </p>
                      
                      <div className="text-xs text-gray-500 mb-4">
                        {category.count} 个服务
                      </div>
                      
                      {/* Popular Services */}
                      <div className="space-y-1">
                        {category.subcategories.slice(0, 3).map((service, index) => (
                          <div
                            key={index}
                            className="text-xs text-gray-500 bg-gray-100 rounded-full px-2 py-1 inline-block mr-1 mb-1"
                          >
                            {service}
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* 所有分类 */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">所有分类</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Link key={category.id} href={`/categories/${category.id}`}>
                <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer h-full">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      {/* Icon */}
                      <div className={`w-12 h-12 rounded-lg ${category.color} flex items-center justify-center text-xl flex-shrink-0`}>
                        {category.icon}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        {/* Category Info */}
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold text-lg text-gray-900 group-hover:text-primary transition-colors truncate">
                            {category.name}
                          </h3>
                          {category.trending && (
                            <Badge variant="destructive" className="text-xs ml-2">
                              热门
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                          {category.description}
                        </p>
                        
                        <div className="text-xs text-gray-500 mb-3">
                          {category.count} 个服务
                        </div>
                        
                        {/* Subcategories */}
                        <div className="space-y-1">
                          {category.subcategories.slice(0, 3).map((service, index) => (
                            <div
                              key={index}
                              className="text-xs text-gray-500 bg-gray-100 rounded-full px-2 py-1 inline-block mr-1 mb-1"
                            >
                              {service}
                            </div>
                          ))}
                          {category.subcategories.length > 3 && (
                            <div className="text-xs text-gray-400">
                              +{category.subcategories.length - 3} 更多
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* 底部提示 */}
        <div className="text-center mt-12 p-8 bg-white rounded-lg shadow-sm">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            没有找到您需要的服务？
          </h3>
          <p className="text-gray-600 mb-4">
            联系我们，我们会尽快为您添加相关服务分类
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="inline-block">
              <button className="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary/90 transition-colors">
                联系我们
              </button>
            </Link>
            <Link href="/services/publish" className="inline-block">
              <button className="border border-primary text-primary px-6 py-2 rounded-md hover:bg-primary/10 transition-colors">
                发布服务
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
