'use client'

import { useState } from 'react'
import { Search, ChevronDown, ChevronRight, HelpCircle, Phone, Mail } from 'lucide-react'
import { Input } from '../../components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null)

  const categories = [
    {
      id: 'getting-started',
      title: '新手入门',
      icon: '🚀',
      questions: [
        {
          question: '如何注册账户？',
          answer: '点击页面右上角的"注册"按钮，填写邮箱、手机号和密码即可完成注册。注册后请验证邮箱以激活账户。'
        },
        {
          question: '如何预订服务？',
          answer: '浏览服务列表，选择合适的服务，点击"立即预订"，填写服务时间和地址，确认订单并支付即可。'
        },
        {
          question: '如何联系服务商？',
          answer: '在服务详情页面可以看到服务商的联系方式，也可以通过平台内消息系统与服务商沟通。'
        }
      ]
    },
    {
      id: 'orders',
      title: '订单相关',
      icon: '📋',
      questions: [
        {
          question: '如何查看订单状态？',
          answer: '登录账户后，在"个人中心"的"我的订单"中可以查看所有订单的状态和详情。'
        },
        {
          question: '可以取消订单吗？',
          answer: '在服务开始前24小时可以免费取消订单。24小时内取消可能需要支付一定的取消费用。'
        },
        {
          question: '如何修改订单信息？',
          answer: '在订单确认前可以直接修改。确认后需要联系服务商协商修改，或联系客服协助处理。'
        }
      ]
    },
    {
      id: 'payment',
      title: '支付问题',
      icon: '💳',
      questions: [
        {
          question: '支持哪些支付方式？',
          answer: '支持微信支付、支付宝、银行卡支付等多种支付方式，选择您方便的方式即可。'
        },
        {
          question: '支付失败怎么办？',
          answer: '请检查网络连接和支付账户余额，如仍有问题请联系客服或尝试其他支付方式。'
        },
        {
          question: '如何申请退款？',
          answer: '在订单页面点击"申请退款"，填写退款原因，客服会在1-3个工作日内处理。'
        }
      ]
    },
    {
      id: 'service-provider',
      title: '服务商相关',
      icon: '👨‍🔧',
      questions: [
        {
          question: '如何成为服务商？',
          answer: '点击"成为服务商"，填写申请表单并上传相关资质证明，审核通过后即可开始接单。'
        },
        {
          question: '服务商认证需要什么材料？',
          answer: '需要身份证、相关技能证书、工作经验证明等材料，具体要求根据服务类别而定。'
        },
        {
          question: '如何提高接单率？',
          answer: '完善个人资料、提高服务质量、及时响应客户、保持良好评价可以提高接单率。'
        }
      ]
    },
    {
      id: 'account',
      title: '账户安全',
      icon: '🔒',
      questions: [
        {
          question: '忘记密码怎么办？',
          answer: '在登录页面点击"忘记密码"，输入注册邮箱，按照邮件提示重置密码。'
        },
        {
          question: '如何修改个人信息？',
          answer: '登录后进入"个人中心"，在"账户设置"中可以修改个人信息。'
        },
        {
          question: '账户被盗用怎么办？',
          answer: '立即联系客服冻结账户，修改密码，检查订单和支付记录，必要时报警处理。'
        }
      ]
    },
    {
      id: 'quality',
      title: '服务质量',
      icon: '⭐',
      questions: [
        {
          question: '对服务不满意怎么办？',
          answer: '可以在订单完成后给出评价和建议，也可以联系客服投诉，我们会协调处理。'
        },
        {
          question: '如何评价服务？',
          answer: '服务完成后，在订单页面可以给服务商打分并写评价，帮助其他用户选择。'
        },
        {
          question: '服务商没有按时到达怎么办？',
          answer: '请及时联系服务商确认情况，如无法联系或无合理解释，可联系客服处理。'
        }
      ]
    }
  ]

  const toggleCategory = (categoryId: string) => {
    setExpandedCategory(expandedCategory === categoryId ? null : categoryId)
  }

  const filteredCategories = categories.map(category => ({
    ...category,
    questions: category.questions.filter(
      q => 
        q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        q.answer.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => 
    searchQuery === '' || 
    category.questions.length > 0 ||
    category.title.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">帮助中心</h1>
          <p className="text-xl text-blue-100 max-w-2xl mx-auto mb-8">
            找到您需要的答案，我们随时为您提供帮助
          </p>
          
          {/* 搜索框 */}
          <div className="max-w-md mx-auto relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="search"
              placeholder="搜索问题..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-white text-gray-900"
            />
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 侧边栏导航 */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <HelpCircle className="h-5 w-5 mr-2" />
                  问题分类
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => toggleCategory(category.id)}
                      className="w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors flex items-center"
                    >
                      <span className="mr-2">{category.icon}</span>
                      <span className="flex-1">{category.title}</span>
                      <span className="text-gray-400">
                        ({category.questions.length})
                      </span>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 联系客服 */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>需要更多帮助？</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <Phone className="h-4 w-4 mr-2" />
                    客服热线
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Mail className="h-4 w-4 mr-2" />
                    邮件咨询
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 主要内容 */}
          <div className="lg:col-span-3">
            {searchQuery && (
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  搜索结果
                </h2>
                <p className="text-gray-600">
                  找到 {filteredCategories.reduce((total, cat) => total + cat.questions.length, 0)} 个相关问题
                </p>
              </div>
            )}

            <div className="space-y-6">
              {filteredCategories.map((category) => (
                <Card key={category.id}>
                  <CardHeader>
                    <button
                      onClick={() => toggleCategory(category.id)}
                      className="w-full flex items-center justify-between text-left"
                    >
                      <CardTitle className="flex items-center">
                        <span className="mr-3 text-2xl">{category.icon}</span>
                        {category.title}
                        <span className="ml-2 text-sm font-normal text-gray-500">
                          ({category.questions.length} 个问题)
                        </span>
                      </CardTitle>
                      {expandedCategory === category.id ? (
                        <ChevronDown className="h-5 w-5" />
                      ) : (
                        <ChevronRight className="h-5 w-5" />
                      )}
                    </button>
                  </CardHeader>
                  
                  {(expandedCategory === category.id || searchQuery) && (
                    <CardContent>
                      <div className="space-y-4">
                        {category.questions.map((qa, index) => (
                          <div key={index} className="border-l-4 border-primary pl-4">
                            <h4 className="font-medium text-gray-900 mb-2">
                              {qa.question}
                            </h4>
                            <p className="text-gray-700 text-sm leading-relaxed">
                              {qa.answer}
                            </p>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>

            {filteredCategories.length === 0 && searchQuery && (
              <Card>
                <CardContent className="text-center py-12">
                  <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    没有找到相关问题
                  </h3>
                  <p className="text-gray-600 mb-4">
                    尝试使用其他关键词搜索，或联系客服获取帮助
                  </p>
                  <Button>联系客服</Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
