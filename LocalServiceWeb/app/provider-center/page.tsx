import { BarChart3, Users, Star, TrendingUp, Calendar, MessageCircle, Settings, HelpCircle } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'

export default function ProviderCenterPage() {
  const stats = [
    {
      title: '本月订单',
      value: '28',
      change: '+12%',
      changeType: 'increase',
      icon: <Calendar className="h-6 w-6" />
    },
    {
      title: '总收入',
      value: '¥12,580',
      change: '+8%',
      changeType: 'increase',
      icon: <TrendingUp className="h-6 w-6" />
    },
    {
      title: '服务评分',
      value: '4.8',
      change: '+0.2',
      changeType: 'increase',
      icon: <Star className="h-6 w-6" />
    },
    {
      title: '客户数量',
      value: '156',
      change: '+15',
      changeType: 'increase',
      icon: <Users className="h-6 w-6" />
    }
  ]

  const recentOrders = [
    {
      id: 'ORD001',
      service: '家电维修',
      customer: '李先生',
      date: '2024-01-15',
      time: '14:00',
      status: 'pending',
      amount: 120
    },
    {
      id: 'ORD002',
      service: '清洁服务',
      customer: '王女士',
      date: '2024-01-14',
      time: '10:00',
      status: 'completed',
      amount: 200
    },
    {
      id: 'ORD003',
      service: '家电维修',
      customer: '张先生',
      date: '2024-01-13',
      time: '16:00',
      status: 'in_progress',
      amount: 80
    }
  ]

  const quickActions = [
    {
      title: '接收新订单',
      description: '查看和接收新的服务订单',
      icon: <Calendar className="h-8 w-8" />,
      color: 'bg-blue-500'
    },
    {
      title: '管理服务',
      description: '编辑和管理您的服务项目',
      icon: <Settings className="h-8 w-8" />,
      color: 'bg-green-500'
    },
    {
      title: '客户消息',
      description: '查看和回复客户消息',
      icon: <MessageCircle className="h-8 w-8" />,
      color: 'bg-purple-500'
    },
    {
      title: '数据分析',
      description: '查看详细的业务数据分析',
      icon: <BarChart3 className="h-8 w-8" />,
      color: 'bg-orange-500'
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>
      case 'in_progress':
        return <Badge className="bg-blue-100 text-blue-800">进行中</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">待确认</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">服务商中心</h1>
              <p className="text-xl text-blue-100">
                欢迎回来，张师傅！管理您的服务和订单
              </p>
            </div>
            <div className="hidden md:block">
              <div className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-4xl">
                👨‍🔧
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <div className="flex items-center">
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      <span className={`ml-2 text-sm ${
                        stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </span>
                    </div>
                  </div>
                  <div className="text-primary">
                    {stat.icon}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容 */}
          <div className="lg:col-span-2 space-y-8">
            {/* 快捷操作 */}
            <Card>
              <CardHeader>
                <CardTitle>快捷操作</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {quickActions.map((action, index) => (
                    <div key={index} className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div className="flex items-start space-x-4">
                        <div className={`${action.color} text-white p-3 rounded-lg`}>
                          {action.icon}
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg mb-1">{action.title}</h3>
                          <p className="text-gray-600 text-sm">{action.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 最近订单 */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>最近订单</CardTitle>
                <Button variant="outline" size="sm">
                  查看全部
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentOrders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-2">
                          <h4 className="font-medium text-gray-900">{order.service}</h4>
                          {getStatusBadge(order.status)}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>订单号：{order.id}</span>
                          <span>客户：{order.customer}</span>
                          <span>时间：{order.date} {order.time}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-primary">¥{order.amount}</div>
                        <Button variant="outline" size="sm" className="mt-2">
                          查看详情
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 收入趋势 */}
            <Card>
              <CardHeader>
                <CardTitle>收入趋势</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                    <p>收入趋势图表</p>
                    <p className="text-sm">显示最近30天的收入变化</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 个人信息 */}
            <Card>
              <CardHeader>
                <CardTitle>个人信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-2xl mx-auto mb-3">
                    👨‍🔧
                  </div>
                  <h3 className="font-semibold text-lg">张师傅</h3>
                  <p className="text-gray-600">家电维修专家</p>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">认证状态</span>
                    <Badge className="bg-green-100 text-green-800">已认证</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">服务评分</span>
                    <span className="font-medium">4.8/5.0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">完成订单</span>
                    <span className="font-medium">156单</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">加入时间</span>
                    <span className="font-medium">2022年3月</span>
                  </div>
                </div>
                <Button variant="outline" className="w-full mt-4">
                  编辑资料
                </Button>
              </CardContent>
            </Card>

            {/* 待处理事项 */}
            <Card>
              <CardHeader>
                <CardTitle>待处理事项</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">新订单</p>
                      <p className="text-xs text-gray-600">3个待确认</p>
                    </div>
                    <Badge variant="outline">3</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">客户消息</p>
                      <p className="text-xs text-gray-600">2条未读</p>
                    </div>
                    <Badge variant="outline">2</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">待评价</p>
                      <p className="text-xs text-gray-600">1个待处理</p>
                    </div>
                    <Badge variant="outline">1</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 帮助支持 */}
            <Card>
              <CardHeader>
                <CardTitle>帮助支持</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <HelpCircle className="h-4 w-4 mr-2" />
                    服务商指南
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    联系客服
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Settings className="h-4 w-4 mr-2" />
                    账户设置
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
