import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Shield, Eye, Lock, Users, Database, Settings } from 'lucide-react'

export default function PrivacyPage() {
  const principles = [
    {
      icon: <Shield className="h-8 w-8" />,
      title: '数据保护',
      description: '我们采用行业标准的安全措施保护您的个人信息'
    },
    {
      icon: <Eye className="h-8 w-8" />,
      title: '透明公开',
      description: '我们明确告知您我们收集和使用个人信息的目的和方式'
    },
    {
      icon: <Lock className="h-8 w-8" />,
      title: '用户控制',
      description: '您有权查看、修改、删除您的个人信息'
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: '最小化原则',
      description: '我们只收集为您提供服务所必需的个人信息'
    }
  ]

  const sections = [
    {
      title: '1. 我们收集的信息',
      content: [
        '账户信息：包括您的姓名、邮箱地址、手机号码、密码等注册信息。',
        '个人资料：包括您的头像、性别、年龄、地址等个人资料信息。',
        '订单信息：包括您的服务预订记录、支付信息、评价内容等。',
        '设备信息：包括您的设备型号、操作系统、IP地址、浏览器类型等。',
        '使用信息：包括您在平台上的浏览记录、搜索记录、点击行为等。',
        '位置信息：在您授权的情况下，我们会收集您的地理位置信息以提供本地化服务。'
      ]
    },
    {
      title: '2. 信息收集方式',
      content: [
        '直接收集：您在注册、使用服务时主动提供的信息。',
        '自动收集：通过Cookie、日志文件等技术自动收集的信息。',
        '第三方提供：通过合作伙伴或第三方服务获得的信息（需经您同意）。',
        '公开来源：从公开可获得的来源收集的信息。'
      ]
    },
    {
      title: '3. 信息使用目的',
      content: [
        '提供服务：为您提供平台服务，包括账户管理、订单处理、客户支持等。',
        '改善服务：分析用户行为，优化产品功能，提升用户体验。',
        '安全保障：检测和防范欺诈、滥用等安全威胁，保护平台和用户安全。',
        '营销推广：在您同意的情况下，向您发送产品信息、优惠活动等营销内容。',
        '法律合规：遵守法律法规要求，配合监管部门调查等。'
      ]
    },
    {
      title: '4. 信息共享与披露',
      content: [
        '服务商：为完成服务，我们会向相关服务商提供必要的订单和联系信息。',
        '合作伙伴：在您同意的情况下，与合作伙伴共享信息以提供更好的服务。',
        '法律要求：根据法律法规要求或政府部门要求披露信息。',
        '安全需要：为保护平台、用户或公众的安全而必须披露的情况。',
        '我们不会向第三方出售、出租或以其他方式转让您的个人信息。'
      ]
    },
    {
      title: '5. 信息存储与安全',
      content: [
        '存储地点：您的个人信息主要存储在中华人民共和国境内的服务器上。',
        '存储期限：我们会在实现收集目的所需的期限内保留您的个人信息。',
        '安全措施：采用加密传输、访问控制、安全审计等技术和管理措施保护信息安全。',
        '数据备份：定期备份数据以防止数据丢失，备份数据同样受到严格保护。'
      ]
    },
    {
      title: '6. 您的权利',
      content: [
        '查询权：您有权查询我们持有的关于您的个人信息。',
        '更正权：您有权要求我们更正不准确或不完整的个人信息。',
        '删除权：在特定情况下，您有权要求我们删除您的个人信息。',
        '限制处理权：您有权要求我们限制对您个人信息的处理。',
        '数据可携权：您有权要求我们将您的个人信息转移给其他服务提供商。',
        '撤回同意权：您有权随时撤回对个人信息处理的同意。'
      ]
    },
    {
      title: '7. Cookie和类似技术',
      content: [
        'Cookie用途：我们使用Cookie来记住您的偏好设置、保持登录状态、分析网站使用情况。',
        'Cookie类型：包括必要Cookie、功能Cookie、分析Cookie和营销Cookie。',
        '管理Cookie：您可以通过浏览器设置管理或禁用Cookie，但这可能影响某些功能的使用。',
        '其他技术：我们还可能使用网络信标、像素标签等类似技术收集信息。'
      ]
    },
    {
      title: '8. 未成年人保护',
      content: [
        '年龄限制：我们的服务主要面向成年人，不满18周岁的用户需在监护人同意下使用。',
        '特殊保护：我们对未成年人的个人信息给予特殊保护，严格限制收集和使用。',
        '监护人权利：监护人有权查看、修改或删除未成年人的个人信息。',
        '如发现在未获得监护人同意的情况下收集了未成年人信息，我们会立即删除。'
      ]
    },
    {
      title: '9. 跨境传输',
      content: [
        '传输原则：我们主要在中国境内处理您的个人信息。',
        '跨境情况：如需跨境传输，我们会确保符合相关法律法规要求。',
        '安全保障：跨境传输时采用合同条款、认证等方式确保信息安全。',
        '用户同意：重要的跨境传输会事先征得您的明确同意。'
      ]
    },
    {
      title: '10. 政策更新',
      content: [
        '更新通知：我们可能会不时更新本隐私政策，更新时会在平台上发布通知。',
        '重大变更：对于重大变更，我们会通过邮件、短信等方式通知您。',
        '继续使用：您继续使用我们的服务即表示同意更新后的隐私政策。',
        '版本管理：我们会保留隐私政策的历史版本供您查阅。'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">隐私政策</h1>
          <p className="text-xl text-blue-100 max-w-2xl mx-auto">
            我们重视您的隐私，本政策说明我们如何收集、使用和保护您的个人信息
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* 更新日期 */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-gray-600 mb-2">最后更新日期：2024年1月1日</p>
                <p className="text-gray-600">生效日期：2024年1月1日</p>
              </div>
            </CardContent>
          </Card>

          {/* 隐私原则 */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>我们的隐私原则</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {principles.map((principle, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="text-primary">
                      {principle.icon}
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg mb-2">{principle.title}</h3>
                      <p className="text-gray-600">{principle.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 前言 */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>前言</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed">
                本隐私政策适用于本地生活服务平台提供的所有服务。我们深知个人信息对您的重要性，
                并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，
                恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、
                最少够用原则、确保安全原则、主体参与原则、公开透明原则等。
              </p>
            </CardContent>
          </Card>

          {/* 政策内容 */}
          <div className="space-y-6">
            {sections.map((section, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-xl">{section.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {section.content.map((paragraph, pIndex) => (
                      <p key={pIndex} className="text-gray-700 leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* 联系我们 */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-6 w-6 mr-2" />
                如何联系我们
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed mb-4">
                如果您对本隐私政策有任何疑问、意见或建议，或者您需要行使相关权利，
                请通过以下方式联系我们：
              </p>
              <div className="space-y-2 text-gray-700">
                <p><strong>隐私保护专员邮箱：</strong><EMAIL></p>
                <p><strong>客服热线：</strong>400-123-4567</p>
                <p><strong>公司地址：</strong>北京市朝阳区xxx大厦</p>
                <p><strong>邮政编码：</strong>100000</p>
              </div>
              <p className="text-gray-700 leading-relaxed mt-4">
                我们将在收到您的请求后15个工作日内回复您的请求。
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
