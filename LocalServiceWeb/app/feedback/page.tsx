'use client'

import { useState } from 'react'
import { MessageCircle, Star, ThumbsUp, ThumbsDown, Send, Lightbulb, Bug, Heart } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { But<PERSON> } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'

export default function FeedbackPage() {
  const [feedbackForm, setFeedbackForm] = useState({
    type: '',
    title: '',
    description: '',
    rating: 0,
    contact: '',
    anonymous: false
  })

  const feedbackTypes = [
    {
      id: 'suggestion',
      title: '功能建议',
      description: '对产品功能的改进建议',
      icon: <Lightbulb className="h-6 w-6" />,
      color: 'bg-yellow-100 text-yellow-600'
    },
    {
      id: 'bug',
      title: '问题反馈',
      description: '遇到的bug或技术问题',
      icon: <Bug className="h-6 w-6" />,
      color: 'bg-red-100 text-red-600'
    },
    {
      id: 'service',
      title: '服务体验',
      description: '对服务质量的评价和建议',
      icon: <Star className="h-6 w-6" />,
      color: 'bg-blue-100 text-blue-600'
    },
    {
      id: 'praise',
      title: '表扬建议',
      description: '对优秀服务商的表扬',
      icon: <Heart className="h-6 w-6" />,
      color: 'bg-green-100 text-green-600'
    }
  ]

  const recentFeedback = [
    {
      id: '1',
      type: 'suggestion',
      title: '希望增加夜间服务选项',
      description: '很多用户晚上才有时间，建议增加夜间服务时段',
      status: 'in_progress',
      date: '2024-01-15',
      likes: 23,
      user: '用户***123'
    },
    {
      id: '2',
      type: 'service',
      title: '服务商响应速度很快',
      description: '张师傅服务态度很好，技术也很专业，值得推荐',
      status: 'completed',
      date: '2024-01-14',
      likes: 15,
      user: '用户***456'
    },
    {
      id: '3',
      type: 'bug',
      title: '支付页面偶尔卡顿',
      description: '在支付时页面有时会卡住，需要刷新才能继续',
      status: 'fixed',
      date: '2024-01-13',
      likes: 8,
      user: '用户***789'
    }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFeedbackForm(prev => ({ ...prev, [name]: checked }))
    } else {
      setFeedbackForm(prev => ({ ...prev, [name]: value }))
    }
  }

  const handleRatingChange = (rating: number) => {
    setFeedbackForm(prev => ({ ...prev, rating }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Feedback submitted:', feedbackForm)
    // 处理提交逻辑
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>
      case 'in_progress':
        return <Badge className="bg-blue-100 text-blue-800">处理中</Badge>
      case 'fixed':
        return <Badge className="bg-purple-100 text-purple-800">已修复</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">意见反馈</h1>
          <p className="text-xl text-blue-100 max-w-2xl mx-auto">
            您的每一个建议都是我们前进的动力，帮助我们提供更好的服务
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 反馈表单 */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageCircle className="h-6 w-6 mr-2" />
                  提交反馈
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* 反馈类型 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      反馈类型 *
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {feedbackTypes.map((type) => (
                        <div
                          key={type.id}
                          className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            feedbackForm.type === type.id
                              ? 'border-primary bg-primary/5'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setFeedbackForm(prev => ({ ...prev, type: type.id }))}
                        >
                          <div className="flex items-start space-x-3">
                            <div className={`p-2 rounded-lg ${type.color}`}>
                              {type.icon}
                            </div>
                            <div>
                              <h3 className="font-medium">{type.title}</h3>
                              <p className="text-sm text-gray-600">{type.description}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 标题 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      反馈标题 *
                    </label>
                    <Input
                      name="title"
                      required
                      placeholder="请简要描述您的反馈"
                      value={feedbackForm.title}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* 详细描述 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      详细描述 *
                    </label>
                    <textarea
                      name="description"
                      required
                      rows={6}
                      placeholder="请详细描述您遇到的问题或建议..."
                      value={feedbackForm.description}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>

                  {/* 评分 */}
                  {feedbackForm.type === 'service' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        服务评分
                      </label>
                      <div className="flex space-x-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <button
                            key={star}
                            type="button"
                            onClick={() => handleRatingChange(star)}
                            className={`p-1 ${
                              star <= feedbackForm.rating
                                ? 'text-yellow-400'
                                : 'text-gray-300'
                            }`}
                          >
                            <Star className="h-6 w-6 fill-current" />
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 联系方式 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      联系方式（可选）
                    </label>
                    <Input
                      name="contact"
                      placeholder="邮箱或手机号，方便我们联系您"
                      value={feedbackForm.contact}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* 匿名选项 */}
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="anonymous"
                      checked={feedbackForm.anonymous}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                    <label className="ml-2 text-sm text-gray-700">
                      匿名提交（不显示用户信息）
                    </label>
                  </div>

                  <Button type="submit" className="w-full" size="lg">
                    <Send className="h-4 w-4 mr-2" />
                    提交反馈
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 反馈统计 */}
            <Card>
              <CardHeader>
                <CardTitle>反馈统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">本月收到反馈</span>
                    <span className="font-bold text-2xl text-primary">156</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">已处理</span>
                    <span className="font-bold text-green-600">142</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">处理中</span>
                    <span className="font-bold text-blue-600">14</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">平均响应时间</span>
                    <span className="font-bold text-purple-600">2小时</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 反馈指南 */}
            <Card>
              <CardHeader>
                <CardTitle>反馈指南</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div>
                    <h4 className="font-medium mb-1">如何写好反馈？</h4>
                    <ul className="space-y-1 text-gray-600">
                      <li>• 描述具体的问题或建议</li>
                      <li>• 提供详细的操作步骤</li>
                      <li>• 说明期望的结果</li>
                      <li>• 附上相关截图（如有）</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">我们的承诺</h4>
                    <ul className="space-y-1 text-gray-600">
                      <li>• 24小时内响应</li>
                      <li>• 认真对待每个反馈</li>
                      <li>• 及时跟进处理进度</li>
                      <li>• 保护用户隐私</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 最近反馈 */}
        <section className="mt-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">最近反馈</h2>
          <div className="space-y-6">
            {recentFeedback.map((feedback) => (
              <Card key={feedback.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-semibold text-lg">{feedback.title}</h3>
                        {getStatusBadge(feedback.status)}
                      </div>
                      <p className="text-gray-700 mb-3">{feedback.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{feedback.user}</span>
                        <span>{feedback.date}</span>
                        <div className="flex items-center">
                          <ThumbsUp className="h-4 w-4 mr-1" />
                          {feedback.likes}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      </div>
    </div>
  )
}
