import { Users, Target, Award, Heart, Shield, Clock } from 'lucide-react'
import { Card, CardContent } from '../../components/ui/card'

export default function AboutPage() {
  const values = [
    {
      icon: <Shield className="h-8 w-8" />,
      title: '安全可靠',
      description: '严格的服务商认证体系，确保每一次服务都安全可靠'
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: '用户至上',
      description: '始终将用户体验放在首位，提供贴心周到的服务'
    },
    {
      icon: <Award className="h-8 w-8" />,
      title: '品质保证',
      description: '精选优质服务商，严格把控服务质量标准'
    },
    {
      icon: <Clock className="h-8 w-8" />,
      title: '高效便捷',
      description: '快速响应用户需求，提供高效便捷的服务体验'
    }
  ]

  const milestones = [
    { year: '2020', event: '平台正式上线，首批服务商入驻' },
    { year: '2021', event: '用户突破10万，服务覆盖20个城市' },
    { year: '2022', event: '完成B轮融资，服务商突破5000家' },
    { year: '2023', event: '推出企业服务，业务范围进一步扩大' },
    { year: '2024', event: '用户突破50万，成为行业领先平台' }
  ]

  const team = [
    {
      name: '张总',
      position: '创始人兼CEO',
      bio: '10年互联网行业经验，致力于用科技改善生活服务',
      avatar: '👨‍💼'
    },
    {
      name: '李总',
      position: '技术总监',
      bio: '资深技术专家，负责平台技术架构和产品研发',
      avatar: '👨‍💻'
    },
    {
      name: '王总',
      position: '运营总监',
      bio: '丰富的运营管理经验，专注用户体验和服务质量',
      avatar: '👩‍💼'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 英雄区域 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">关于我们</h1>
          <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
            连接优质服务商与用户，让生活更美好
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        {/* 公司介绍 */}
        <section className="mb-16">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">我们的使命</h2>
            <p className="text-lg text-gray-700 leading-relaxed mb-8">
              本地生活服务平台成立于2020年，致力于打造中国领先的本地生活服务平台。
              我们通过先进的技术和严格的服务标准，连接优质的服务商与有需求的用户，
              为用户提供便捷、可靠、高品质的生活服务体验。
            </p>
            <p className="text-lg text-gray-700 leading-relaxed">
              我们相信，科技能够让生活更美好。通过我们的平台，用户可以轻松找到身边的优质服务，
              服务商也能够更好地展示自己的专业技能，实现双赢。
            </p>
          </div>
        </section>

        {/* 核心价值观 */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">核心价值观</h2>
            <p className="text-lg text-gray-600">指导我们前进的核心理念</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="text-primary mb-4 flex justify-center">
                    {value.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 发展历程 */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">发展历程</h2>
            <p className="text-lg text-gray-600">见证我们的成长足迹</p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <div key={index} className="flex items-center">
                  <div className="flex-shrink-0 w-20 h-20 bg-primary text-white rounded-full flex items-center justify-center font-bold text-lg">
                    {milestone.year}
                  </div>
                  <div className="ml-6 flex-1">
                    <div className="bg-white rounded-lg p-6 shadow-md">
                      <p className="text-gray-700">{milestone.event}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 团队介绍 */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">核心团队</h2>
            <p className="text-lg text-gray-600">经验丰富的专业团队</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {team.map((member, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center text-white text-3xl mx-auto mb-4">
                    {member.avatar}
                  </div>
                  <h3 className="text-xl font-semibold mb-1">{member.name}</h3>
                  <p className="text-primary font-medium mb-3">{member.position}</p>
                  <p className="text-gray-600 text-sm">{member.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 数据统计 */}
        <section className="bg-white rounded-lg shadow-sm p-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">平台数据</h2>
            <p className="text-lg text-gray-600">用数字见证我们的成长</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">50万+</div>
              <div className="text-gray-600">注册用户</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">5000+</div>
              <div className="text-gray-600">认证服务商</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">100万+</div>
              <div className="text-gray-600">完成订单</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-2">4.8</div>
              <div className="text-gray-600">平均评分</div>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
