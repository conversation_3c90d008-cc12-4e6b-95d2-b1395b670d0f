'use client'

import { useState } from 'react'
import { CheckCircle, Users, TrendingUp, Shield, Clock, Star, ArrowRight } from 'lucide-react'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'

export default function BecomeProviderPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    category: '',
    experience: '',
    description: ''
  })

  const benefits = [
    {
      icon: <Users className="h-8 w-8" />,
      title: '海量客户',
      description: '接触数万潜在客户，扩大业务范围'
    },
    {
      icon: <TrendingUp className="h-8 w-8" />,
      title: '增加收入',
      description: '平均服务商月收入提升40%'
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: '平台保障',
      description: '完善的保险体系，保障您的权益'
    },
    {
      icon: <Clock className="h-8 w-8" />,
      title: '灵活时间',
      description: '自由安排工作时间，工作生活平衡'
    }
  ]

  const steps = [
    {
      step: 1,
      title: '提交申请',
      description: '填写基本信息和服务类别'
    },
    {
      step: 2,
      title: '资质审核',
      description: '上传相关证件和资质证明'
    },
    {
      step: 3,
      title: '技能认证',
      description: '通过平台技能测试或面试'
    },
    {
      step: 4,
      title: '开始接单',
      description: '审核通过后即可开始接单赚钱'
    }
  ]

  const categories = [
    '家庭维修', '清洁服务', '美容美发', '家教培训', 
    '宠物服务', '搬家服务', '摄影摄像', '健身运动'
  ]

  const successStories = [
    {
      name: '张师傅',
      category: '家电维修',
      income: '月收入 ¥8,000+',
      rating: 4.9,
      orders: 156,
      story: '加入平台2年，从兼职到全职，现在每月稳定收入8000+，客户都很满意。'
    },
    {
      name: '李阿姨',
      category: '清洁服务',
      income: '月收入 ¥6,500+',
      rating: 4.8,
      orders: 203,
      story: '通过平台找到了稳定的客源，工作时间灵活，收入比以前提高了很多。'
    }
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Provider application:', formData)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 英雄区域 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            成为服务商
          </h1>
          <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
            加入我们的平台，开启您的创业之路。数万客户等待您的专业服务
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-orange-500 hover:bg-orange-600 text-white px-8">
              立即申请
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              了解更多
            </Button>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        {/* 平台优势 */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">为什么选择我们</h2>
            <p className="text-lg text-gray-600">专业平台，助您事业腾飞</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="text-primary mb-4 flex justify-center">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 申请流程 */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">申请流程</h2>
            <p className="text-lg text-gray-600">简单4步，快速入驻</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="text-center relative">
                <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h3 className="text-lg font-semibold mb-2">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
                {index < steps.length - 1 && (
                  <ArrowRight className="hidden md:block absolute top-8 -right-4 h-6 w-6 text-gray-400" />
                )}
              </div>
            ))}
          </div>
        </section>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* 申请表单 */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>立即申请成为服务商</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      姓名 *
                    </label>
                    <Input
                      name="name"
                      required
                      placeholder="请输入您的姓名"
                      value={formData.name}
                      onChange={handleInputChange}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱 *
                    </label>
                    <Input
                      name="email"
                      type="email"
                      required
                      placeholder="请输入邮箱地址"
                      value={formData.email}
                      onChange={handleInputChange}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      手机号 *
                    </label>
                    <Input
                      name="phone"
                      type="tel"
                      required
                      placeholder="请输入手机号码"
                      value={formData.phone}
                      onChange={handleInputChange}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服务类别 *
                    </label>
                    <select
                      name="category"
                      required
                      value={formData.category}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">请选择服务类别</option>
                      {categories.map((category) => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      从业经验 *
                    </label>
                    <select
                      name="experience"
                      required
                      value={formData.experience}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">请选择从业经验</option>
                      <option value="1年以下">1年以下</option>
                      <option value="1-3年">1-3年</option>
                      <option value="3-5年">3-5年</option>
                      <option value="5年以上">5年以上</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服务描述
                    </label>
                    <textarea
                      name="description"
                      rows={4}
                      placeholder="请简单描述您的服务内容和优势"
                      value={formData.description}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>
                  
                  <Button type="submit" className="w-full" size="lg">
                    提交申请
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* 成功案例 */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">成功案例</h3>
            <div className="space-y-6">
              {successStories.map((story, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl font-bold">
                        {story.name.charAt(0)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-lg font-semibold">{story.name}</h4>
                          <Badge>{story.category}</Badge>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                          <span className="font-medium text-green-600">{story.income}</span>
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                            {story.rating}
                          </div>
                          <span>{story.orders}单</span>
                        </div>
                        <p className="text-gray-700 italic">"{story.story}"</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* 常见问题 */}
            <div className="mt-8">
              <h3 className="text-xl font-bold text-gray-900 mb-4">常见问题</h3>
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 border">
                  <h4 className="font-medium mb-2">申请需要什么条件？</h4>
                  <p className="text-gray-600 text-sm">需要有相关技能和经验，通过平台审核即可。</p>
                </div>
                <div className="bg-white rounded-lg p-4 border">
                  <h4 className="font-medium mb-2">平台收取多少佣金？</h4>
                  <p className="text-gray-600 text-sm">平台收取订单金额的10%作为服务费。</p>
                </div>
                <div className="bg-white rounded-lg p-4 border">
                  <h4 className="font-medium mb-2">多久可以开始接单？</h4>
                  <p className="text-gray-600 text-sm">审核通过后即可开始接单，通常3-5个工作日。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
