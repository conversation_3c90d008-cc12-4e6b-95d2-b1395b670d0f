import { Suspense } from 'react'
import { Search, Filter, MapPin, Star, Grid, List } from 'lucide-react'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Card, CardContent } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'

export default function ServicesPage() {
  const services = [
    {
      id: '1',
      title: '专业家电维修服务',
      description: '提供各类家电维修服务，包括空调、洗衣机、冰箱等。拥有10年维修经验，技术过硬，价格合理。',
      price: 80,
      priceUnit: '小时',
      rating: 4.8,
      reviewCount: 156,
      category: '家庭维修',
      location: '朝阳区',
      image: '/api/placeholder/400/300',
      tags: ['上门服务', '质量保证', '价格透明'],
      provider: {
        name: '张师傅',
        rating: 4.9,
        isVerified: true,
      }
    },
    {
      id: '2',
      title: '深度清洁服务',
      description: '专业清洁团队，提供家庭、办公室深度清洁服务。使用环保清洁用品，细致入微。',
      price: 120,
      priceUnit: '次',
      rating: 4.9,
      reviewCount: 203,
      category: '清洁服务',
      location: '海淀区',
      image: '/api/placeholder/400/300',
      tags: ['环保用品', '专业团队', '细致服务'],
      provider: {
        name: '李阿姨',
        rating: 4.8,
        isVerified: true,
      }
    },
    {
      id: '3',
      title: '上门理发服务',
      description: '专业理发师上门服务，方便快捷。提供男女老少各种发型设计，工具齐全。',
      price: 60,
      priceUnit: '次',
      rating: 4.7,
      reviewCount: 89,
      category: '美容美发',
      location: '西城区',
      image: '/api/placeholder/400/300',
      tags: ['上门服务', '专业技术', '工具齐全'],
      provider: {
        name: '王师傅',
        rating: 4.6,
        isVerified: false,
      }
    },
    // 更多服务...
  ]

  const categories = [
    '全部', '家庭维修', '清洁服务', '美容美发', '家教培训', '宠物服务', '搬家服务'
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">找服务</h1>
          <p className="text-gray-600">发现您身边的优质服务</p>
        </div>

        {/* 搜索和筛选 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* 搜索框 */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="search"
                placeholder="搜索服务..."
                className="pl-10"
              />
            </div>
            
            {/* 位置筛选 */}
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="选择位置..."
                className="pl-10 w-full lg:w-48"
              />
            </div>
            
            {/* 筛选按钮 */}
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              筛选
            </Button>
            
            {/* 视图切换 */}
            <div className="flex border rounded-lg">
              <Button variant="ghost" size="sm" className="rounded-r-none">
                <Grid className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="rounded-l-none">
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* 侧边栏筛选 */}
          <div className="lg:w-64">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="font-semibold text-lg mb-4">服务分类</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    className="block w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    {category}
                  </button>
                ))}
              </div>
              
              <hr className="my-6" />
              
              <h3 className="font-semibold text-lg mb-4">价格范围</h3>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">¥0 - ¥50</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">¥50 - ¥100</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">¥100 - ¥200</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">¥200+</span>
                </label>
              </div>
              
              <hr className="my-6" />
              
              <h3 className="font-semibold text-lg mb-4">评分</h3>
              <div className="space-y-2">
                {[5, 4, 3].map((rating) => (
                  <label key={rating} className="flex items-center">
                    <input type="checkbox" className="mr-2" />
                    <div className="flex items-center">
                      {[...Array(rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                      ))}
                      <span className="text-sm ml-1">及以上</span>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* 服务列表 */}
          <div className="flex-1">
            <div className="flex justify-between items-center mb-6">
              <p className="text-gray-600">找到 {services.length} 个服务</p>
              <select className="border rounded-md px-3 py-2">
                <option>默认排序</option>
                <option>价格从低到高</option>
                <option>价格从高到低</option>
                <option>评分最高</option>
                <option>最新发布</option>
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {services.map((service) => (
                <Card key={service.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="aspect-video bg-gray-200 rounded-t-lg relative">
                    <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center rounded-t-lg">
                      <span className="text-4xl opacity-50">📷</span>
                    </div>
                    <Badge className="absolute top-3 left-3">
                      {service.category}
                    </Badge>
                  </div>
                  
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-lg line-clamp-1">
                        {service.title}
                      </h3>
                      <div className="text-right">
                        <div className="text-lg font-bold text-primary">
                          ¥{service.price}
                        </div>
                        <div className="text-sm text-gray-500">
                          /{service.priceUnit}
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {service.description}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 mr-1" />
                        {service.rating} ({service.reviewCount}评价)
                      </div>
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        {service.location}
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mb-3">
                      {service.tags.slice(0, 2).map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs mr-2">
                          {service.provider.name.charAt(0)}
                        </div>
                        <span className="text-sm font-medium">{service.provider.name}</span>
                      </div>
                      <Button size="sm">查看详情</Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            {/* 分页 */}
            <div className="flex justify-center mt-8">
              <div className="flex space-x-2">
                <Button variant="outline" disabled>上一页</Button>
                <Button variant="outline" className="bg-primary text-white">1</Button>
                <Button variant="outline">2</Button>
                <Button variant="outline">3</Button>
                <Button variant="outline">下一页</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
