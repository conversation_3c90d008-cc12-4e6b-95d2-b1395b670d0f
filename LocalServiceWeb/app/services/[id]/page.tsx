import { Star, MapPin, Clock, Shield, Phone, MessageCircle, Heart, Share2 } from 'lucide-react'
import { Button } from '../../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Badge } from '../../../components/ui/badge'

interface ServiceDetailPageProps {
  params: {
    id: string
  }
}

export default function ServiceDetailPage({ params }: ServiceDetailPageProps) {
  // 模拟数据
  const service = {
    id: params.id,
    title: '专业家电维修服务',
    description: '提供各类家电维修服务，包括空调、洗衣机、冰箱、电视、微波炉等。拥有10年维修经验，技术过硬，价格合理。我们承诺使用原厂配件，提供质保服务，让您用得放心。',
    price: 80,
    priceUnit: '小时',
    rating: 4.8,
    reviewCount: 156,
    orderCount: 89,
    category: '家庭维修',
    location: '朝阳区',
    images: ['/api/placeholder/600/400', '/api/placeholder/600/400', '/api/placeholder/600/400'],
    tags: ['上门服务', '质量保证', '价格透明', '原厂配件'],
    features: [
      '免费上门检测',
      '原厂配件保证',
      '30天质保服务',
      '24小时响应',
      '价格透明公开',
      '专业技术团队'
    ],
    provider: {
      id: '1',
      name: '张师傅',
      avatar: '👨‍🔧',
      rating: 4.9,
      reviewCount: 234,
      serviceCount: 45,
      isVerified: true,
      joinDate: '2020-03-15',
      responseTime: '30分钟内',
      bio: '从事家电维修行业10年，擅长各类家电故障诊断和维修，服务过上千个家庭。'
    },
    serviceArea: ['朝阳区', '海淀区', '东城区', '西城区'],
    workingHours: '周一至周日 8:00-20:00',
    reviews: [
      {
        id: '1',
        user: { name: '李女士', avatar: '👩' },
        rating: 5,
        comment: '师傅很专业，修好了我家的空调，价格也很合理。服务态度很好，会推荐给朋友！',
        date: '2024-01-10',
        images: []
      },
      {
        id: '2',
        user: { name: '王先生', avatar: '👨' },
        rating: 5,
        comment: '技术很好，很快就找到了问题所在，维修效率很高。',
        date: '2024-01-08',
        images: []
      }
    ]
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 服务图片 */}
            <Card>
              <CardContent className="p-0">
                <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 rounded-t-lg flex items-center justify-center">
                  <span className="text-6xl opacity-50">📷</span>
                </div>
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h1 className="text-2xl font-bold text-gray-900 mb-2">
                        {service.title}
                      </h1>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-400 mr-1" />
                          {service.rating} ({service.reviewCount}评价)
                        </div>
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          {service.location}
                        </div>
                        <span>{service.orderCount}人已预订</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Heart className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Share2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    <Badge>{service.category}</Badge>
                    {service.tags.map((tag, index) => (
                      <Badge key={index} variant="outline">{tag}</Badge>
                    ))}
                  </div>
                  
                  <p className="text-gray-700 leading-relaxed">
                    {service.description}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* 服务特色 */}
            <Card>
              <CardHeader>
                <CardTitle>服务特色</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {service.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 服务商信息 */}
            <Card>
              <CardHeader>
                <CardTitle>服务商信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start space-x-4">
                  <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-2xl">
                    {service.provider.avatar}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-lg font-semibold">{service.provider.name}</h3>
                      {service.provider.isVerified && (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <Shield className="w-3 h-3 mr-1" />
                          已认证
                        </Badge>
                      )}
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                      <div>
                        <div className="font-medium">服务评分</div>
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-400 mr-1" />
                          {service.provider.rating}
                        </div>
                      </div>
                      <div>
                        <div className="font-medium">服务次数</div>
                        <div>{service.provider.serviceCount}次</div>
                      </div>
                      <div>
                        <div className="font-medium">响应时间</div>
                        <div>{service.provider.responseTime}</div>
                      </div>
                      <div>
                        <div className="font-medium">加入时间</div>
                        <div>2020年</div>
                      </div>
                    </div>
                    <p className="text-gray-700 text-sm">{service.provider.bio}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 用户评价 */}
            <Card>
              <CardHeader>
                <CardTitle>用户评价 ({service.reviewCount})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {service.reviews.map((review) => (
                    <div key={review.id} className="border-b border-gray-200 last:border-b-0 pb-6 last:pb-0">
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white">
                          {review.user.avatar}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="font-medium">{review.user.name}</span>
                            <div className="flex">
                              {[...Array(review.rating)].map((_, i) => (
                                <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                              ))}
                            </div>
                            <span className="text-sm text-gray-500">{review.date}</span>
                          </div>
                          <p className="text-gray-700">{review.comment}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-6 text-center">
                  <Button variant="outline">查看更多评价</Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 价格和预订 */}
            <Card>
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-primary mb-1">
                    ¥{service.price}
                  </div>
                  <div className="text-gray-600">/{service.priceUnit}</div>
                </div>
                
                <div className="space-y-3 mb-6">
                  <Button className="w-full" size="lg">
                    立即预订
                  </Button>
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" className="flex items-center justify-center">
                      <Phone className="w-4 h-4 mr-2" />
                      电话
                    </Button>
                    <Button variant="outline" className="flex items-center justify-center">
                      <MessageCircle className="w-4 h-4 mr-2" />
                      咨询
                    </Button>
                  </div>
                </div>
                
                <div className="text-sm text-gray-600 space-y-2">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-2" />
                    {service.workingHours}
                  </div>
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-2" />
                    服务区域：{service.serviceArea.join('、')}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 服务保障 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">服务保障</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center">
                    <Shield className="w-4 h-4 text-green-500 mr-2" />
                    <span>实名认证</span>
                  </div>
                  <div className="flex items-center">
                    <Shield className="w-4 h-4 text-green-500 mr-2" />
                    <span>服务保险</span>
                  </div>
                  <div className="flex items-center">
                    <Shield className="w-4 h-4 text-green-500 mr-2" />
                    <span>质量保证</span>
                  </div>
                  <div className="flex items-center">
                    <Shield className="w-4 h-4 text-green-500 mr-2" />
                    <span>售后支持</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
