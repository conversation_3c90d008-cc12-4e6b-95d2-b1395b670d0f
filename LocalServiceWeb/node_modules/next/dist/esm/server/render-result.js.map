{"version": 3, "sources": ["../../src/server/render-result.ts"], "names": ["chainStreams", "streamFromString", "streamToString", "isAbortError", "pipeToNodeResponse", "RenderResult", "fromStatic", "value", "metadata", "constructor", "response", "contentType", "waitUntil", "assignMetadata", "Object", "assign", "isNull", "isDynamic", "toUnchunkedString", "stream", "Error", "readable", "Array", "isArray", "chain", "responses", "push", "pipeTo", "writable", "preventClose", "close", "err", "abort", "res"], "mappings": "AAIA,SACEA,YAAY,EACZC,gBAAgB,EAChBC,cAAc,QACT,yCAAwC;AAC/C,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAiB;AAqDlE,eAAe,MAAMC;IAuBnB;;;;;GAKC,GACD,OAAcC,WAAWC,KAAa,EAAE;QACtC,OAAO,IAAIF,aAAyCE,OAAO;YAAEC,UAAU,CAAC;QAAE;IAC5E;IAIAC,YACEC,QAA8B,EAC9B,EAAEC,WAAW,EAAEC,SAAS,EAAEJ,QAAQ,EAAiC,CACnE;QACA,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACC,WAAW,GAAGA;QACnB,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACI,SAAS,GAAGA;IACnB;IAEOC,eAAeL,QAAkB,EAAE;QACxCM,OAAOC,MAAM,CAAC,IAAI,CAACP,QAAQ,EAAEA;IAC/B;IAEA;;;GAGC,GACD,IAAWQ,SAAkB;QAC3B,OAAO,IAAI,CAACN,QAAQ,KAAK;IAC3B;IAEA;;;GAGC,GACD,IAAWO,YAAqB;QAC9B,OAAO,OAAO,IAAI,CAACP,QAAQ,KAAK;IAClC;IAWOQ,kBAAkBC,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIU,MAAM;QAClB;QAEA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACS,QAAQ;gBACX,MAAM,IAAIC,MACR;YAEJ;YAEA,OAAOlB,eAAe,IAAI,CAACmB,QAAQ;QACrC;QAEA,OAAO,IAAI,CAACX,QAAQ;IACtB;IAEA;;;GAGC,GACD,IAAYW,WAAuC;QACjD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIU,MAAM;QAClB;QACA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,MAAM,IAAIU,MAAM;QAClB;QAEA,oEAAoE;QACpE,IAAIE,MAAMC,OAAO,CAAC,IAAI,CAACb,QAAQ,GAAG;YAChC,OAAOV,gBAAgB,IAAI,CAACU,QAAQ;QACtC;QAEA,OAAO,IAAI,CAACA,QAAQ;IACtB;IAEA;;;;;;;GAOC,GACD,AAAOc,MAAMH,QAAoC,EAAE;QACjD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1B,MAAM,IAAIU,MAAM;QAClB;QAEA,mEAAmE;QACnE,IAAIK;QACJ,IAAI,OAAO,IAAI,CAACf,QAAQ,KAAK,UAAU;YACrCe,YAAY;gBAACxB,iBAAiB,IAAI,CAACS,QAAQ;aAAE;QAC/C,OAAO,IAAIY,MAAMC,OAAO,CAAC,IAAI,CAACb,QAAQ,GAAG;YACvCe,YAAY,IAAI,CAACf,QAAQ;QAC3B,OAAO;YACLe,YAAY;gBAAC,IAAI,CAACf,QAAQ;aAAC;QAC7B;QAEA,mCAAmC;QACnCe,UAAUC,IAAI,CAACL;QAEf,uBAAuB;QACvB,IAAI,CAACX,QAAQ,GAAGe;IAClB;IAEA;;;;;;GAMC,GACD,MAAaE,OAAOC,QAAoC,EAAiB;QACvE,IAAI;YACF,MAAM,IAAI,CAACP,QAAQ,CAACM,MAAM,CAACC,UAAU;gBACnC,qEAAqE;gBACrE,sEAAsE;gBACtE,sEAAsE;gBACtE,SAAS;gBACTC,cAAc;YAChB;YAEA,iEAAiE;YACjE,+BAA+B;YAC/B,IAAI,IAAI,CAACjB,SAAS,EAAE,MAAM,IAAI,CAACA,SAAS;YAExC,6BAA6B;YAC7B,MAAMgB,SAASE,KAAK;QACtB,EAAE,OAAOC,KAAK;YACZ,wEAAwE;YACxE,0EAA0E;YAC1E,gCAAgC;YAChC,IAAI5B,aAAa4B,MAAM;gBACrB,wDAAwD;gBACxD,MAAMH,SAASI,KAAK,CAACD;gBAErB;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,MAAMA;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAa3B,mBAAmB6B,GAAmB,EAAE;QACnD,MAAM7B,mBAAmB,IAAI,CAACiB,QAAQ,EAAEY,KAAK,IAAI,CAACrB,SAAS;IAC7D;AACF"}