{"version": 3, "sources": ["../../../../../src/server/future/route-modules/helpers/response-handlers.ts"], "names": ["appendMutableCookies", "handleRedirectResponse", "url", "mutableCookies", "status", "headers", "Headers", "location", "Response", "handleBadRequestResponse", "handleNotFoundResponse", "handleMethodNotAllowedResponse", "handleInternalServerErrorResponse"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,uDAAsD;AAG3F,OAAO,SAASC,uBACdC,GAAW,EACXC,cAA+B,EAC/BC,MAAc;IAEd,MAAMC,UAAU,IAAIC,QAAQ;QAAEC,UAAUL;IAAI;IAE5CF,qBAAqBK,SAASF;IAE9B,OAAO,IAAIK,SAAS,MAAM;QAAEJ;QAAQC;IAAQ;AAC9C;AAEA,OAAO,SAASI;IACd,OAAO,IAAID,SAAS,MAAM;QAAEJ,QAAQ;IAAI;AAC1C;AAEA,OAAO,SAASM;IACd,OAAO,IAAIF,SAAS,MAAM;QAAEJ,QAAQ;IAAI;AAC1C;AAEA,OAAO,SAASO;IACd,OAAO,IAAIH,SAAS,MAAM;QAAEJ,QAAQ;IAAI;AAC1C;AAEA,OAAO,SAASQ;IACd,OAAO,IAAIJ,SAAS,MAAM;QAAEJ,QAAQ;IAAI;AAC1C"}