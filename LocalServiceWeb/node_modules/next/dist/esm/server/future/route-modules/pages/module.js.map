{"version": 3, "sources": ["../../../../../src/server/future/route-modules/pages/module.ts"], "names": ["RouteModule", "renderToHTMLImpl", "renderToHTML", "vendoredContexts", "PagesRouteModule", "constructor", "options", "components", "render", "req", "res", "context", "page", "query", "renderOpts", "App", "Document", "vendored", "contexts"], "mappings": "AAcA,SACEA,WAAW,QAGN,kBAAiB;AACxB,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,kBAAiB;AAChE,YAAYC,sBAAsB,kCAAiC;AAuFnE,OAAO,MAAMC,yBAAyBJ;IAMpCK,YAAYC,OAAgC,CAAE;QAC5C,KAAK,CAACA;QAEN,IAAI,CAACC,UAAU,GAAGD,QAAQC,UAAU;IACtC;IAEOC,OACLC,GAAoB,EACpBC,GAAmB,EACnBC,OAAiC,EACV;QACvB,OAAOV,iBACLQ,KACAC,KACAC,QAAQC,IAAI,EACZD,QAAQE,KAAK,EACbF,QAAQG,UAAU,EAClB;YACEC,KAAK,IAAI,CAACR,UAAU,CAACQ,GAAG;YACxBC,UAAU,IAAI,CAACT,UAAU,CAACS,QAAQ;QACpC;IAEJ;AACF;AAEA,MAAMC,WAAW;IACfC,UAAUf;AACZ;AAEA,8BAA8B;AAC9B,SAASD,YAAY,EAAEe,QAAQ,GAAE;AAEjC,eAAeb,iBAAgB"}