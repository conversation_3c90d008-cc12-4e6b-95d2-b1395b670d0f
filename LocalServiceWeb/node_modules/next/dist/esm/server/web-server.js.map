{"version": 3, "sources": ["../../src/server/web-server.ts"], "names": ["byteLength", "BaseServer", "NoFallbackError", "generateETag", "addRequestMeta", "WebResponseCache", "isAPIRoute", "removeTrailingSlash", "isDynamicRoute", "interpolateDynamicPath", "normalizeVercelUrl", "normalizeDynamicRouteParams", "getNamedRouteRegex", "getRouteMatcher", "IncrementalCache", "buildCustomRoute", "UNDERSCORE_NOT_FOUND_ROUTE", "NextWebServer", "constructor", "options", "handleCatchallRenderRequest", "req", "res", "parsedUrl", "pathname", "query", "Error", "normalizedPage", "serverOptions", "webServerConfig", "routeRegex", "dynamicRouteMatcher", "defaultRouteMatches", "paramsResult", "normalizedParams", "hasValidParams", "params", "Object", "keys", "routeKeys", "i18nProvider", "detectedLocale", "analyze", "__next<PERSON><PERSON><PERSON>", "bubbleNoFallback", "_nextBubbleNoFallback", "render", "err", "assign", "renderOpts", "extendRenderOpts", "getIncrementalCache", "requestHeaders", "dev", "requestProtocol", "pagesDir", "enabledDirectories", "pages", "appDir", "app", "allowedRevalidateHeaderKeys", "nextConfig", "experimental", "minimalMode", "fetchCache", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "cacheMaxMemorySize", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "getPrerenderManifest", "ppr", "getResponseCache", "hasPage", "page", "getBuildId", "buildId", "getEnabledDirectories", "pagesType", "getPagesManifest", "getAppPathsManifest", "attachRequestMeta", "prerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "getNextFontManifest", "nextFontManifest", "renderHTML", "renderToHTML", "disableOptimizedLoading", "runtime", "sendRenderResult", "_req", "<PERSON><PERSON><PERSON><PERSON>", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "result", "contentType", "promise", "isDynamic", "pipeTo", "transformStream", "writable", "payload", "toUnchunkedString", "String", "generateEtags", "body", "send", "findPageComponents", "url", "_url", "loadComponent", "components", "run<PERSON><PERSON>", "handleApiRequest", "loadEnvConfig", "getPublicDir", "getHasStaticDir", "get<PERSON>allback", "getFontManifest", "undefined", "handleCompression", "handleUpgrade", "getFallbackErrorComponents", "getRoutesManifest", "getMiddleware", "getFilesystemPaths", "Set", "getPrefetchRsc", "getinterceptionRoutePatterns", "interceptionRouteRewrites", "map", "rewrite", "RegExp", "regex"], "mappings": "AAeA,SAASA,UAAU,QAAQ,kBAAiB;AAC5C,OAAOC,cAAcC,eAAe,QAAQ,gBAAe;AAC3D,SAASC,YAAY,QAAQ,aAAY;AACzC,SAASC,cAAc,QAAQ,iBAAgB;AAC/C,OAAOC,sBAAsB,uBAAsB;AACnD,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SACEC,sBAAsB,EACtBC,kBAAkB,EAClBC,2BAA2B,QACtB,iBAAgB;AACvB,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,gBAAgB,QAAQ,0BAAyB;AAG1D,SAASC,gBAAgB,QAAQ,4BAA2B;AAC5D,SAASC,0BAA0B,QAAQ,mBAAkB;AAqB7D,eAAe,MAAMC,sBAAsBhB;IACzCiB,YAAYC,OAAyB,CAAE;QACrC,KAAK,CAACA;aAgGEC,8BAA4C,OACpDC,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE,GAAGF;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,IAAIE,MAAM;YAClB;YAEA,4DAA4D;YAC5D,+CAA+C;YAC/C,MAAMC,iBAAiB,IAAI,CAACC,aAAa,CAACC,eAAe,CAACL,QAAQ;YAElE,IAAIA,aAAaG,gBAAgB;gBAC/BH,WAAWG;gBAEX,IAAInB,eAAegB,WAAW;oBAC5B,MAAMM,aAAalB,mBAAmBY,UAAU;oBAChD,MAAMO,sBAAsBlB,gBAAgBiB;oBAC5C,MAAME,sBAAsBD,oBAC1BP;oBAEF,MAAMS,eAAetB,4BACnBc,OACA,OACAK,YACAE;oBAEF,MAAME,mBAAmBD,aAAaE,cAAc,GAChDF,aAAaG,MAAM,GACnBX;oBAEJD,WAAWf,uBACTe,UACAU,kBACAJ;oBAEFpB,mBACEW,KACA,MACAgB,OAAOC,IAAI,CAACR,WAAWS,SAAS,GAChC,MACAT;gBAEJ;YACF;YAEA,wDAAwD;YACxDN,WAAWjB,oBAAoBiB;YAE/B,IAAI,IAAI,CAACgB,YAAY,EAAE;gBACrB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACD,YAAY,CAACE,OAAO,CAAClB;gBAC3D,IAAIiB,gBAAgB;oBAClBlB,UAAUE,KAAK,CAACkB,YAAY,GAAGF;gBACjC;YACF;YAEA,MAAMG,mBAAmB,CAAC,CAACnB,MAAMoB,qBAAqB;YAEtD,IAAIvC,WAAWkB,WAAW;gBACxB,OAAOC,MAAMoB,qBAAqB;YACpC;YAEA,IAAI;gBACF,MAAM,IAAI,CAACC,MAAM,CAACzB,KAAKC,KAAKE,UAAUC,OAAOF,WAAW;gBAExD,OAAO;YACT,EAAE,OAAOwB,KAAK;gBACZ,IAAIA,eAAe7C,mBAAmB0C,kBAAkB;oBACtD,OAAO;gBACT;gBACA,MAAMG;YACR;QACF;QAxKE,uBAAuB;QACvBV,OAAOW,MAAM,CAAC,IAAI,CAACC,UAAU,EAAE9B,QAAQU,eAAe,CAACqB,gBAAgB;IACzE;IAEA,MAAgBC,oBAAoB,EAClCC,cAAc,EAGf,EAAE;QACD,MAAMC,MAAM,CAAC,CAAC,IAAI,CAACJ,UAAU,CAACI,GAAG;QACjC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIvC,iBAAiB;YAC1BuC;YACAD;YACAE,iBAAiB;YACjBC,UAAU,IAAI,CAACC,kBAAkB,CAACC,KAAK;YACvCC,QAAQ,IAAI,CAACF,kBAAkB,CAACG,GAAG;YACnCC,6BACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACF,2BAA2B;YAC1DG,aAAa,IAAI,CAACA,WAAW;YAC7BC,YAAY;YACZC,qBAAqB,IAAI,CAACJ,UAAU,CAACC,YAAY,CAACG,mBAAmB;YACrEC,oBAAoB,IAAI,CAACL,UAAU,CAACM,kBAAkB;YACtDC,aAAa;YACbC,iBACE,IAAI,CAACzC,aAAa,CAACC,eAAe,CAACyC,uBAAuB;YAC5DC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;YACrD,4CAA4C;YAC5CT,cAAc;gBAAEU,KAAK;YAAM;QAC7B;IACF;IACUC,mBAAmB;QAC3B,OAAO,IAAIpE,iBAAiB,IAAI,CAAC0D,WAAW;IAC9C;IAEA,MAAgBW,QAAQC,IAAY,EAAE;QACpC,OAAOA,SAAS,IAAI,CAAC/C,aAAa,CAACC,eAAe,CAAC8C,IAAI;IACzD;IAEUC,aAAa;QACrB,OAAO,IAAI,CAAChD,aAAa,CAACC,eAAe,CAACqB,gBAAgB,CAAC2B,OAAO;IACpE;IAEUC,wBAAwB;QAChC,OAAO;YACLnB,KAAK,IAAI,CAAC/B,aAAa,CAACC,eAAe,CAACkD,SAAS,KAAK;YACtDtB,OAAO,IAAI,CAAC7B,aAAa,CAACC,eAAe,CAACkD,SAAS,KAAK;QAC1D;IACF;IAEUC,mBAAmB;QAC3B,OAAO;YACL,8DAA8D;YAC9D,CAAC,IAAI,CAACpD,aAAa,CAACC,eAAe,CAChCL,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAACI,aAAa,CAACC,eAAe,CAAC8C,IAAI,CAAC,GAAG,CAAC;QACrE;IACF;IAEUM,sBAAsB;QAC9B,MAAMN,OAAO,IAAI,CAAC/C,aAAa,CAACC,eAAe,CAAC8C,IAAI;QACpD,OAAO;YACL,CAAC,IAAI,CAAC/C,aAAa,CAACC,eAAe,CAAC8C,IAAI,CAAC,EAAE,CAAC,GAAG,EAAEA,KAAK,GAAG,CAAC;QAC5D;IACF;IAEUO,kBACR7D,GAAmB,EACnBE,SAAiC,EACjC;QACAnB,eAAeiB,KAAK,aAAa;YAAE,GAAGE,UAAUE,KAAK;QAAC;IACxD;IAEU8C,uBAAuB;YAE3B;QADJ,MAAM,EAAEY,iBAAiB,EAAE,GAAG,IAAI,CAACvD,aAAa,CAACC,eAAe;QAChE,IAAI,EAAA,mBAAA,IAAI,CAACoB,UAAU,qBAAf,iBAAiBI,GAAG,KAAI,CAAC8B,mBAAmB;YAC9C,OAAO;gBACLC,SAAS,CAAC;gBACVC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,gBAAgB,EAAE;gBAClBC,SAAS;oBACPC,eAAe;gBACjB;YACF;QACF;QACA,OAAON;IACT;IAEUO,sBAAsB;QAC9B,OAAO,IAAI,CAAC9D,aAAa,CAACC,eAAe,CAACqB,gBAAgB,CAACyC,gBAAgB;IAC7E;IA8EUC,WACRvE,GAAmB,EACnBC,GAAoB,EACpBE,QAAgB,EAChBC,KAAyB,EACzBwB,UAA4B,EACL;QACvB,MAAM,EAAE4C,YAAY,EAAE,GAAG,IAAI,CAACjE,aAAa,CAACC,eAAe;QAC3D,IAAI,CAACgE,cAAc;YACjB,MAAM,IAAInE,MACR;QAEJ;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,IAAIF,aAAaR,4BAA4B;YAC3CQ,WAAW;QACb;QACA,OAAOqE,aACLxE,KACAC,KACAE,UACAC,OACAY,OAAOW,MAAM,CAACC,YAAY;YACxB6C,yBAAyB;YACzBC,SAAS;QACX;IAEJ;IAEA,MAAgBC,iBACdC,IAAoB,EACpB3E,GAAoB,EACpBH,OAOC,EACc;QACfG,IAAI4E,SAAS,CAAC,kBAAkB;QAEhC,yBAAyB;QACzB,iEAAiE;QACjE,IAAI/E,QAAQgF,eAAe,IAAIhF,QAAQiF,IAAI,KAAK,QAAQ;YACtD9E,IAAI4E,SAAS,CAAC,gBAAgB;QAChC;QAEA,IAAI,CAAC5E,IAAI+E,SAAS,CAAC,iBAAiB;YAClC/E,IAAI4E,SAAS,CACX,gBACA/E,QAAQmF,MAAM,CAACC,WAAW,GACtBpF,QAAQmF,MAAM,CAACC,WAAW,GAC1BpF,QAAQiF,IAAI,KAAK,SACjB,qBACA;QAER;QAEA,IAAII;QACJ,IAAIrF,QAAQmF,MAAM,CAACG,SAAS,EAAE;YAC5BD,UAAUrF,QAAQmF,MAAM,CAACI,MAAM,CAACpF,IAAIqF,eAAe,CAACC,QAAQ;QAC9D,OAAO;YACL,MAAMC,UAAU1F,QAAQmF,MAAM,CAACQ,iBAAiB;YAChDxF,IAAI4E,SAAS,CAAC,kBAAkBa,OAAO/G,WAAW6G;YAClD,IAAI1F,QAAQ6F,aAAa,EAAE;gBACzB1F,IAAI4E,SAAS,CAAC,QAAQ/F,aAAa0G;YACrC;YACAvF,IAAI2F,IAAI,CAACJ;QACX;QAEAvF,IAAI4F,IAAI;QAER,gDAAgD;QAChD,IAAIV,SAAS,MAAMA;IACrB;IAEA,MAAgBW,mBAAmB,EACjCxC,IAAI,EACJlD,KAAK,EACLW,MAAM,EACNgF,KAAKC,IAAI,EAOV,EAAE;QACD,MAAMf,SAAS,MAAM,IAAI,CAAC1E,aAAa,CAACC,eAAe,CAACyF,aAAa,CAAC3C;QACtE,IAAI,CAAC2B,QAAQ,OAAO;QAEpB,OAAO;YACL7E,OAAO;gBACL,GAAIA,SAAS,CAAC,CAAC;gBACf,GAAIW,UAAU,CAAC,CAAC;YAClB;YACAmF,YAAYjB;QACd;IACF;IAEA,2EAA2E;IAC3E,+DAA+D;IAE/D,MAAgBkB,SAAS;QACvB,wDAAwD;QACxD,OAAO;IACT;IAEA,MAAgBC,mBAAmB;QACjC,4DAA4D;QAC5D,OAAO;IACT;IAEUC,gBAAgB;IACxB,2EAA2E;IAC3E,mBAAmB;IACrB;IAEUC,eAAe;QACvB,kDAAkD;QAClD,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO;IACT;IAEA,MAAgBC,cAAc;QAC5B,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAOC;IACT;IAEUC,oBAAoB;IAC5B,wEAAwE;IACxE,4EAA4E;IAC9E;IAEA,MAAgBC,gBAA+B;IAC7C,+CAA+C;IACjD;IAEA,MAAgBC,2BACdb,IAAa,EAC6B;QAC1C,wEAAwE;QACxE,OAAO;IACT;IACUc,oBAAyD;QACjE,4EAA4E;QAC5E,gDAAgD;QAChD,OAAOJ;IACT;IAEUK,gBAAmD;QAC3D,yEAAyE;QACzE,gDAAgD;QAChD,OAAOL;IACT;IAEUM,qBAAqB;QAC7B,OAAO,IAAIC;IACb;IAEA,MAAgBC,iBAAyC;QACvD,OAAO;IACT;IAEUC,+BAAyC;YAE/C;QADF,OACE,EAAA,gEAAA,IAAI,CAAC5G,aAAa,CAACC,eAAe,CAAC4G,yBAAyB,qBAA5D,8DAA8DC,GAAG,CAC/D,CAACC,UAAY,IAAIC,OAAO7H,iBAAiB,WAAW4H,SAASE,KAAK,OAC/D,EAAE;IAEX;AACF"}