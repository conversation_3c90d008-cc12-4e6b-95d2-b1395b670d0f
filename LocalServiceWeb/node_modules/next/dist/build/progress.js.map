{"version": 3, "sources": ["../../src/build/progress.ts"], "names": ["createProgress", "divideSegments", "number", "segments", "result", "dividedNumber", "Math", "floor", "push", "total", "label", "Error", "currentSegmentTotal", "shift", "currentSegmentCount", "lastProgressOutput", "Date", "now", "curProgress", "progressSpinner", "createSpinner", "spinner", "frames", "interval", "isFinished", "message", "setText", "stop", "Log", "event", "info", "process", "stdout", "isTTY"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;6DAhBQ;gEACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1B,SAASC,eAAeC,MAAc,EAAEC,QAAgB;IACtD,MAAMC,SAAS,EAAE;IACjB,MAAOF,SAAS,KAAKC,WAAW,EAAG;QACjC,MAAME,gBACJH,SAASC,WAAWD,SAASI,KAAKC,KAAK,CAACL,SAASC;QAEnDD,UAAUG;QACVF;QACAC,OAAOI,IAAI,CAACH;IACd;IACA,OAAOD;AACT;AAEO,MAAMJ,iBAAiB,CAACS,OAAeC;IAC5C,MAAMP,WAAWF,eAAeQ,OAAO;IAEvC,IAAIA,UAAU,GAAG;QACf,MAAM,IAAIE,MAAM;IAClB;IACA,IAAIC,sBAAsBT,SAASU,KAAK;IACxC,IAAIC,sBAAsB;IAC1B,IAAIC,qBAAqBC,KAAKC,GAAG;IACjC,IAAIC,cAAc;IAClB,IAAIC,kBAAkBC,IAAAA,gBAAa,EAAC,CAAC,EAAEV,MAAM,EAAE,EAAEQ,YAAY,CAAC,EAAET,MAAM,CAAC,CAAC,EAAE;QACxEY,SAAS;YACPC,QAAQ;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACDC,UAAU;QACZ;IACF;IAEA,OAAO;QACLL;QAEA,6BAA6B;QAC7B,oCAAoC;QACpC,eAAe;QACf,+BAA+B;QAC/B,IAAI,CAACC,iBAAiB;YACpBL;YAEA,IAAIA,wBAAwBF,qBAAqB;gBAC/CA,sBAAsBT,SAASU,KAAK;gBACpCC,sBAAsB;YACxB,OAAO,IAAIC,qBAAqB,QAAQC,KAAKC,GAAG,IAAI;gBAClD;YACF;YAEAF,qBAAqBC,KAAKC,GAAG;QAC/B;QAEA,MAAMO,aAAaN,gBAAgBT;QACnC,MAAMgB,UAAU,CAAC,EAAEf,MAAM,EAAE,EAAEQ,YAAY,CAAC,EAAET,MAAM,CAAC,CAAC;QACpD,IAAIU,mBAAmB,CAACK,YAAY;YAClCL,gBAAgBO,OAAO,CAACD;QAC1B,OAAO;YACLN,mCAAAA,gBAAiBQ,IAAI;YACrB,IAAIH,YAAY;gBACdI,KAAIC,KAAK,CAACJ;YACZ,OAAO;gBACLG,KAAIE,IAAI,CAAC,CAAC,EAAEL,QAAQ,CAAC,EAAEM,QAAQC,MAAM,CAACC,KAAK,GAAG,OAAO,KAAK,CAAC;YAC7D;QACF;IACF;AACF"}