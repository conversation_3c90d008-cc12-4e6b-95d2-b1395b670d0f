{"version": 3, "sources": ["../../src/build/spinner.ts"], "names": ["createSpinner", "dots<PERSON>pinner", "frames", "interval", "text", "options", "logFn", "console", "log", "spinner", "prefixText", "Log", "prefixes", "info", "process", "stdout", "isTTY", "ora", "undefined", "stream", "start", "origLog", "origWarn", "warn", "origError", "error", "origStop", "stop", "bind", "origStopAndPersist", "stopAndPersist", "logHandle", "method", "args", "resetLog", "setText", "newText", "suffixText", "event"], "mappings": ";;;;+BAQA;;;eAAwBA;;;4DARR;6DACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,cAAc;IAClBC,QAAQ;QAAC;QAAK;QAAM;KAAM;IAC1BC,UAAU;AACZ;AAEe,SAASH,cACtBI,IAAY,EACZC,UAAuB,CAAC,CAAC,EACzBC,QAAkCC,QAAQC,GAAG;IAE7C,IAAIC;IAEJ,IAAIC,aAAa,CAAC,CAAC,EAAEC,KAAIC,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAET,KAAK,CAAC,CAAC;IAEjD,IAAIU,QAAQC,MAAM,CAACC,KAAK,EAAE;QACxBP,UAAUQ,IAAAA,YAAG,EAAC;YACZb,MAAMc;YACNR;YACAD,SAASR;YACTkB,QAAQL,QAAQC,MAAM;YACtB,GAAGV,OAAO;QACZ,GAAGe,KAAK;QAER,2DAA2D;QAC3D,+DAA+D;QAC/D,MAAMC,UAAUd,QAAQC,GAAG;QAC3B,MAAMc,WAAWf,QAAQgB,IAAI;QAC7B,MAAMC,YAAYjB,QAAQkB,KAAK;QAC/B,MAAMC,WAAWjB,QAAQkB,IAAI,CAACC,IAAI,CAACnB;QACnC,MAAMoB,qBAAqBpB,QAAQqB,cAAc,CAACF,IAAI,CAACnB;QAEvD,MAAMsB,YAAY,CAACC,QAAaC;YAC9BP;YACAM,UAAUC;YACVxB,QAASW,KAAK;QAChB;QAEAb,QAAQC,GAAG,GAAG,CAAC,GAAGyB,OAAcF,UAAUV,SAASY;QACnD1B,QAAQgB,IAAI,GAAG,CAAC,GAAGU,OAAcF,UAAUT,UAAUW;QACrD1B,QAAQkB,KAAK,GAAG,CAAC,GAAGQ,OAAcF,UAAUP,WAAWS;QAEvD,MAAMC,WAAW;YACf3B,QAAQC,GAAG,GAAGa;YACdd,QAAQgB,IAAI,GAAGD;YACff,QAAQkB,KAAK,GAAGD;QAClB;QACAf,QAAQ0B,OAAO,GAAG,CAACC;YACjBhC,OAAOgC;YACP1B,aAAa,CAAC,CAAC,EAAEC,KAAIC,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAEuB,QAAQ,CAAC,CAAC;YAChD3B,QAASC,UAAU,GAAGA;YACtB,OAAOD;QACT;QACAA,QAAQkB,IAAI,GAAG;YACbD;YACAQ;YACA,OAAOzB;QACT;QACAA,QAAQqB,cAAc,GAAG;YACvB,uEAAuE;YACvE,MAAMO,aAAa,CAAC,GAAG,EAAE1B,KAAIC,QAAQ,CAAC0B,KAAK,CAAC,CAAC,EAAElC,KAAK,CAAC,CAAC;YACtD,IAAIK,SAAS;gBACXA,QAAQL,IAAI,GAAGiC;YACjB,OAAO;gBACL/B,MAAM+B;YACR;YACAR;YACAK;YACA,OAAOzB;QACT;IACF,OAAO,IAAIC,cAAcN,MAAM;QAC7BE,MAAMI,aAAaA,aAAa,QAAQN;IAC1C;IAEA,OAAOK;AACT"}