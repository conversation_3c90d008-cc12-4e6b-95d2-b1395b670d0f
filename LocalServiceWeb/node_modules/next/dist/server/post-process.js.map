{"version": 3, "sources": ["../../src/server/post-process.ts"], "names": ["postProcessHTML", "middlewareRegistry", "registerPostProcessor", "name", "middleware", "condition", "push", "processHTML", "html", "data", "options", "parse", "require", "root", "document", "callMiddleWare", "inspectData", "inspect", "mutate", "i", "length", "FontOptimizerMiddleware", "originalDom", "getFontDefinition", "fontDefinitions", "querySelectorAll", "filter", "tag", "getAttribute", "hasAttribute", "OPTIMIZED_FONT_PROVIDERS", "some", "url", "dataHref", "startsWith", "for<PERSON>ach", "element", "nonce", "markup", "result", "preconnectUrls", "Set", "fontDef", "fallBackLinkTag", "indexOf", "fontContent", "replace", "nonceStr", "dataAttr", "includes", "escapedUrl", "fontRegex", "RegExp", "provider", "find", "p", "add", "preconnect", "preconnectTag", "pathname", "content", "renderOpts", "inAmpMode", "hybridAmp", "postProcessors", "process", "env", "NEXT_RUNTIME", "optimizeAmp", "default", "ampOptimizerConfig", "ampSkipValidation", "ampValidator", "optimizeFonts", "fontManifest", "font", "optimizeCss", "Critters", "cssOptimizer", "ssrMode", "reduceInlineStyles", "path", "distDir", "publicPath", "assetPrefix", "preload", "fonts", "nonNullable", "postProcessor", "__NEXT_OPTIMIZE_FONTS"], "mappings": ";;;;+BAiRSA;;;eAAAA;;;2BA9QgC;6BACb;AAwB5B,MAAMC,qBAAiD,EAAE;AAEzD,SAASC,sBACPC,IAAY,EACZC,UAAiC,EACjCC,SAAoD;IAEpDJ,mBAAmBK,IAAI,CAAC;QAAEH;QAAMC;QAAYC,WAAWA,aAAa;IAAK;AAC3E;AAEA,eAAeE,YACbC,IAAY,EACZC,IAAmB,EACnBC,OAA2B;IAE3B,+DAA+D;IAC/D,IAAI,CAACT,kBAAkB,CAAC,EAAE,EAAE;QAC1B,OAAOO;IACT;IAEA,MAAM,EAAEG,KAAK,EAAE,GACbC,QAAQ;IACV,MAAMC,OAAoBF,MAAMH;IAChC,IAAIM,WAAWN;IAEf,8DAA8D;IAC9D,eAAeO,eAAeX,UAAiC;QAC7D,yBAAyB;QACzB,MAAMY,cAAcZ,WAAWa,OAAO,CAACJ,MAAMJ;QAC7CK,WAAW,MAAMV,WAAWc,MAAM,CAACJ,UAAUE,aAAaP;QAC1D,6BAA6B;QAC7B,wCAAwC;QACxC,gEAAgE;QAChE,gDAAgD;QAChD,IAAI;QACJ;IACF;IAEA,IAAK,IAAIU,IAAI,GAAGA,IAAIlB,mBAAmBmB,MAAM,EAAED,IAAK;QAClD,IAAIf,aAAaH,kBAAkB,CAACkB,EAAE;QACtC,IAAI,CAACf,WAAWC,SAAS,IAAID,WAAWC,SAAS,CAACK,UAAU;YAC1D,MAAMK,eAAed,kBAAkB,CAACkB,EAAE,CAACf,UAAU;QACvD;IACF;IAEA,OAAOU;AACT;AAEA,MAAMO;IACJJ,QAAQK,WAAwB,EAAEZ,OAAsB,EAAE;QACxD,IAAI,CAACA,QAAQa,iBAAiB,EAAE;YAC9B;QACF;QACA,MAAMC,kBAA4C,EAAE;QACpD,gDAAgD;QAChDF,YACGG,gBAAgB,CAAC,QACjBC,MAAM,CACL,CAACC,MACCA,IAAIC,YAAY,CAAC,WAAW,gBAC5BD,IAAIE,YAAY,CAAC,gBACjBC,mCAAwB,CAACC,IAAI,CAAC,CAAC,EAAEC,GAAG,EAAE;gBACpC,MAAMC,WAAWN,IAAIC,YAAY,CAAC;gBAClC,OAAOK,WAAWA,SAASC,UAAU,CAACF,OAAO;YAC/C,IAEHG,OAAO,CAAC,CAACC;YACR,MAAMJ,MAAMI,QAAQR,YAAY,CAAC;YACjC,MAAMS,QAAQD,QAAQR,YAAY,CAAC;YAEnC,IAAII,KAAK;gBACPR,gBAAgBlB,IAAI,CAAC;oBAAC0B;oBAAKK;iBAAM;YACnC;QACF;QAEF,OAAOb;IACT;;aACAN,SAAS,OACPoB,QACAd,iBACAd;YAEA,IAAI6B,SAASD;YACb,IAAIE,iBAAiB,IAAIC;YAEzB,IAAI,CAAC/B,QAAQa,iBAAiB,EAAE;gBAC9B,OAAOe;YACT;YAEAd,gBAAgBW,OAAO,CAAC,CAACO;gBACvB,MAAM,CAACV,KAAKK,MAAM,GAAGK;gBACrB,MAAMC,kBAAkB,CAAC,6BAA6B,EAAEX,IAAI,GAAG,CAAC;gBAChE,IACEO,OAAOK,OAAO,CAAC,CAAC,kBAAkB,EAAEZ,IAAI,EAAE,CAAC,IAAI,CAAC,KAChDO,OAAOK,OAAO,CAACD,mBAAmB,CAAC,GACnC;oBACA,oEAAoE;oBACpE;gBACF;gBACA,MAAME,cAAcnC,QAAQa,iBAAiB,GACzCb,QAAQa,iBAAiB,CAACS,OAC1B;gBACJ,IAAI,CAACa,aAAa;oBAChB;;SAEC,GACDN,SAASA,OAAOO,OAAO,CAAC,WAAW,CAAC,EAAEH,gBAAgB,OAAO,CAAC;gBAChE,OAAO;oBACL,MAAMI,WAAWV,QAAQ,CAAC,QAAQ,EAAEA,MAAM,CAAC,CAAC,GAAG;oBAC/C,IAAIW,WAAW;oBAEf,IAAIH,YAAYI,QAAQ,CAAC,oBAAoB;wBAC3CD,WAAW;oBACb;oBAEAT,SAASA,OAAOO,OAAO,CACrB,WACA,CAAC,kBAAkB,EAAEd,IAAI,CAAC,EAAEe,SAAS,EAAEC,SAAS,CAAC,EAAEH,YAAY,eAAe,CAAC;oBAGjF,wBAAwB;oBACxB,MAAMK,aAAalB,IAChBc,OAAO,CAAC,MAAM,SACdA,OAAO,CAAC,uBAAuB;oBAClC,MAAMK,YAAY,IAAIC,OACpB,CAAC,qBAAqB,EAAEF,WAAW,QAAQ,CAAC;oBAE9CX,SAASA,OAAOO,OAAO,CAACK,WAAW;oBAEnC,MAAME,WAAWvB,mCAAwB,CAACwB,IAAI,CAAC,CAACC,IAC9CvB,IAAIE,UAAU,CAACqB,EAAEvB,GAAG;oBAGtB,IAAIqB,UAAU;wBACZb,eAAegB,GAAG,CAACH,SAASI,UAAU;oBACxC;gBACF;YACF;YAEA,IAAIC,gBAAgB;YACpBlB,eAAeL,OAAO,CAAC,CAACH;gBACtB0B,iBAAiB,CAAC,6BAA6B,EAAE1B,IAAI,gBAAgB,CAAC;YACxE;YAEAO,SAASA,OAAOO,OAAO,CACrB,uCACAY;YAGF,OAAOnB;QACT;;AACF;AAEA,eAAevC,gBACb2D,QAAgB,EAChBC,OAAe,EACfC,UAUC,EACD,EAAEC,SAAS,EAAEC,SAAS,EAA8C;IAEpE,MAAMC,iBAA+C;QACnDC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL,YACnC,OAAOtD;YACL,MAAM4D,cAAcxD,QAAQ,kBACzByD,OAAO;YACV7D,OAAO,MAAM4D,YAAa5D,MAAMqD,WAAWS,kBAAkB;YAC7D,IAAI,CAACT,WAAWU,iBAAiB,IAAIV,WAAWW,YAAY,EAAE;gBAC5D,MAAMX,WAAWW,YAAY,CAAChE,MAAMmD;YACtC;YACA,OAAOnD;QACT,IACA;QACJyD,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUN,WAAWY,aAAa,GAC3D,OAAOjE;YACL,MAAMe,oBAAoB,CAACS;oBAKvB6B;gBAJF,IAAI,CAACA,WAAWa,YAAY,EAAE;oBAC5B,OAAO;gBACT;gBACA,OACEb,EAAAA,gCAAAA,WAAWa,YAAY,CAACpB,IAAI,CAAC,CAACqB;oBAC5B,IAAIA,QAAQA,KAAK3C,GAAG,KAAKA,KAAK;wBAC5B,OAAO;oBACT;oBACA,OAAO;gBACT,uBALA6B,8BAKID,OAAO,KAAI;YAEnB;YACA,OAAO,MAAMrD,YACXC,MACA;gBAAEe;YAAkB,GACpB;gBACEkD,eAAeZ,WAAWY,aAAa;YACzC;QAEJ,IACA;QACJR,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUN,WAAWe,WAAW,GACzD,OAAOpE;YACL,6DAA6D;YAC7D,MAAMqE,WAAWjE,QAAQ;YACzB,MAAMkE,eAAe,IAAID,SAAS;gBAChCE,SAAS;gBACTC,oBAAoB;gBACpBC,MAAMpB,WAAWqB,OAAO;gBACxBC,YAAY,CAAC,EAAEtB,WAAWuB,WAAW,CAAC,OAAO,CAAC;gBAC9CC,SAAS;gBACTC,OAAO;gBACP,GAAGzB,WAAWe,WAAW;YAC3B;YACA,OAAO,MAAME,aAAab,OAAO,CAACzD;QACpC,IACA;QACJsD,aAAaC,YACT,CAACvD;YACC,OAAOA,KAAKsC,OAAO,CAAC,eAAe;QACrC,IACA;KACL,CAACpB,MAAM,CAAC6D,wBAAW;IAEpB,KAAK,MAAMC,iBAAiBxB,eAAgB;QAC1C,IAAIwB,eAAe;YACjB5B,UAAU,MAAM4B,cAAc5B;QAChC;IACF;IACA,OAAOA;AACT;AAEA,iBAAiB;AACjB1D,sBACE,gBACA,IAAImB,2BACJ,sFAAsF;AACtF,aAAa;AACb,CAACX,UAAYA,QAAQ+D,aAAa,IAAIR,QAAQC,GAAG,CAACuB,qBAAqB"}