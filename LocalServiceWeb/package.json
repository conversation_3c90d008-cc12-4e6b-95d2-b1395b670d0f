{"name": "local-service-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "@supabase/supabase-js": "^2.50.0", "@hookform/resolvers": "^3.3.4", "react-hook-form": "^7.51.5", "zod": "^3.23.8", "zustand": "^4.5.2", "lucide-react": "^0.400.0", "framer-motion": "^11.2.10", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "date-fns": "^3.6.0", "clsx": "^2.1.1", "tailwind-merge": "^2.3.0", "class-variance-authority": "^0.7.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-badge": "^1.0.4", "recharts": "^2.12.7"}, "devDependencies": {"typescript": "^5.5.2", "@types/node": "^20.14.8", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/leaflet": "^1.9.12", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "tailwindcss": "^3.4.4", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13"}}