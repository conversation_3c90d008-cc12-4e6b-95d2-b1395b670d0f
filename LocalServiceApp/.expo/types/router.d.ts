/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/booking-result`; params?: Router.UnknownInputParams; } | { pathname: `/favorites`; params?: Router.UnknownInputParams; } | { pathname: `/feedback`; params?: Router.UnknownInputParams; } | { pathname: `/help`; params?: Router.UnknownInputParams; } | { pathname: `/my-reviews`; params?: Router.UnknownInputParams; } | { pathname: `/my-services`; params?: Router.UnknownInputParams; } | { pathname: `/payment`; params?: Router.UnknownInputParams; } | { pathname: `/profile-edit`; params?: Router.UnknownInputParams; } | { pathname: `/publish-service`; params?: Router.UnknownInputParams; } | { pathname: `/search`; params?: Router.UnknownInputParams; } | { pathname: `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/wallet`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/messages` | `/messages`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/booking/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/category/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/chat/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/order/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/provider/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/rate-service/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/reviews/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/service/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/booking-result`; params?: Router.UnknownOutputParams; } | { pathname: `/favorites`; params?: Router.UnknownOutputParams; } | { pathname: `/feedback`; params?: Router.UnknownOutputParams; } | { pathname: `/help`; params?: Router.UnknownOutputParams; } | { pathname: `/my-reviews`; params?: Router.UnknownOutputParams; } | { pathname: `/my-services`; params?: Router.UnknownOutputParams; } | { pathname: `/payment`; params?: Router.UnknownOutputParams; } | { pathname: `/profile-edit`; params?: Router.UnknownOutputParams; } | { pathname: `/publish-service`; params?: Router.UnknownOutputParams; } | { pathname: `/search`; params?: Router.UnknownOutputParams; } | { pathname: `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/wallet`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/messages` | `/messages`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/booking/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/category/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/chat/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/order/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/provider/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/rate-service/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/reviews/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/service/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/booking-result${`?${string}` | `#${string}` | ''}` | `/favorites${`?${string}` | `#${string}` | ''}` | `/feedback${`?${string}` | `#${string}` | ''}` | `/help${`?${string}` | `#${string}` | ''}` | `/my-reviews${`?${string}` | `#${string}` | ''}` | `/my-services${`?${string}` | `#${string}` | ''}` | `/payment${`?${string}` | `#${string}` | ''}` | `/profile-edit${`?${string}` | `#${string}` | ''}` | `/publish-service${`?${string}` | `#${string}` | ''}` | `/search${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `/wallet${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/explore${`?${string}` | `#${string}` | ''}` | `/explore${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/messages${`?${string}` | `#${string}` | ''}` | `/messages${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/orders${`?${string}` | `#${string}` | ''}` | `/orders${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/booking-result`; params?: Router.UnknownInputParams; } | { pathname: `/favorites`; params?: Router.UnknownInputParams; } | { pathname: `/feedback`; params?: Router.UnknownInputParams; } | { pathname: `/help`; params?: Router.UnknownInputParams; } | { pathname: `/my-reviews`; params?: Router.UnknownInputParams; } | { pathname: `/my-services`; params?: Router.UnknownInputParams; } | { pathname: `/payment`; params?: Router.UnknownInputParams; } | { pathname: `/profile-edit`; params?: Router.UnknownInputParams; } | { pathname: `/publish-service`; params?: Router.UnknownInputParams; } | { pathname: `/search`; params?: Router.UnknownInputParams; } | { pathname: `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/wallet`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/messages` | `/messages`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/orders` | `/orders`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | `/+not-found` | `/booking/${Router.SingleRoutePart<T>}` | `/category/${Router.SingleRoutePart<T>}` | `/chat/${Router.SingleRoutePart<T>}` | `/order/${Router.SingleRoutePart<T>}` | `/provider/${Router.SingleRoutePart<T>}` | `/rate-service/${Router.SingleRoutePart<T>}` | `/reviews/${Router.SingleRoutePart<T>}` | `/service/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/booking/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/category/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/chat/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/order/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/provider/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/rate-service/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/reviews/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/service/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
